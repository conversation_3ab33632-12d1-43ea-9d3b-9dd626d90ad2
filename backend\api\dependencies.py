"""
FastAPI dependencies for the Advanced Conversational AI Sales Assistant
"""
from typing import Dict, Any, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from snowflake.snowpark import Session
import jwt
import logging

from config import settings

logger = logging.getLogger(__name__)
security = HTTPBearer()

# Global Snowflake session (in production, use connection pooling)
_snowflake_session: Optional[Session] = None


def get_snowflake_session() -> Session:
    """Get Snowflake session dependency"""
    global _snowflake_session
    
    if _snowflake_session is None:
        try:
            connection_parameters = {
                "account": settings.snowflake.account,
                "user": settings.snowflake.user,
                "password": settings.snowflake.password,
                "warehouse": settings.snowflake.warehouse,
                "database": settings.snowflake.database,
                "schema": settings.snowflake.schema,
            }
            
            if settings.snowflake.role:
                connection_parameters["role"] = settings.snowflake.role
            
            _snowflake_session = Session.builder.configs(connection_parameters).create()
            logger.info("Snowflake session created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create Snowflake session: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database connection failed"
            )
    
    return _snowflake_session


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """Get current user from JWT token"""
    try:
        # Decode JWT token
        payload = jwt.decode(
            credentials.credentials,
            settings.app.secret_key,
            algorithms=["HS256"]
        )
        
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # In a real application, you would fetch user details from database
        user_data = {
            "user_id": user_id,
            "username": payload.get("username"),
            "email": payload.get("email"),
            "role": payload.get("role", "user"),
            "permissions": payload.get("permissions", [])
        }
        
        return user_data
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_admin_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Dependency for admin-only endpoints"""
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


class PermissionChecker:
    """Permission checker for role-based access control"""
    
    def __init__(self, required_permission: str):
        self.required_permission = required_permission
    
    def __call__(self, current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
        user_permissions = current_user.get("permissions", [])
        if self.required_permission not in user_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{self.required_permission}' required"
            )
        return current_user


# Common permission dependencies
require_crm_access = PermissionChecker("crm:read")
require_crm_write = PermissionChecker("crm:write")
require_report_access = PermissionChecker("reports:read")
require_proposal_access = PermissionChecker("proposals:write")
require_contract_access = PermissionChecker("contracts:read")
require_voice_access = PermissionChecker("voice:process")
require_coaching_access = PermissionChecker("coaching:read")
require_intelligence_access = PermissionChecker("intelligence:read")


def validate_file_upload(file_size: int, allowed_types: list) -> bool:
    """Validate file upload parameters"""
    if file_size > settings.app.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.app.max_file_size} bytes"
        )
    return True


async def get_rate_limit_key(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> str:
    """Get rate limiting key for user"""
    return f"user:{current_user.get('user_id')}"


class DatabaseManager:
    """Database connection and transaction manager"""
    
    def __init__(self, session: Session = Depends(get_snowflake_session)):
        self.session = session
    
    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Any:
        """Execute a query with error handling"""
        try:
            if params:
                result = self.session.sql(query, params).collect()
            else:
                result = self.session.sql(query).collect()
            return result
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database operation failed"
            )
    
    async def execute_transaction(self, queries: list) -> bool:
        """Execute multiple queries in a transaction"""
        try:
            # Snowflake doesn't have explicit transactions in Snowpark
            # This is a placeholder for transaction-like behavior
            for query in queries:
                self.session.sql(query).collect()
            return True
        except Exception as e:
            logger.error(f"Transaction failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Transaction failed"
            )


def get_db_manager(session: Session = Depends(get_snowflake_session)) -> DatabaseManager:
    """Get database manager dependency"""
    return DatabaseManager(session)


# Cache dependencies (using Redis in production)
class CacheManager:
    """Cache manager for storing temporary data"""
    
    def __init__(self):
        # In production, this would connect to Redis
        self._cache = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        return self._cache.get(key)
    
    async def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        """Set value in cache with TTL"""
        self._cache[key] = value
        # In production, implement TTL with Redis
        return True
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        if key in self._cache:
            del self._cache[key]
            return True
        return False


_cache_manager = CacheManager()


def get_cache_manager() -> CacheManager:
    """Get cache manager dependency"""
    return _cache_manager
