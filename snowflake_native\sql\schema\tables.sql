-- Advanced Conversational AI Sales Assistant - 100% Snowflake Native
-- Database Schema for Nihilent x Snowflake Hackathon 2025

-- Create database and schema
CREATE DATABASE IF NOT EXISTS SALES_AI_NATIVE;
USE DATABASE SALES_AI_NATIVE;
CREATE SCHEMA IF NOT EXISTS CORE;
USE SCHEMA CORE;

-- Enable Snowflake Cortex AI
-- (Requires ACCOUNTADMIN privileges)

-- =============================================
-- CORE BUSINESS TABLES
-- =============================================

-- Users table
CREATE OR REPLACE TABLE users (
    user_id STRING PRIMARY KEY,
    username STRING UNIQUE NOT NULL,
    email STRING UNIQUE NOT NULL,
    full_name STRING,
    role STRING DEFAULT 'sales_rep',
    department STRING,
    manager_id STRING,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Companies/Accounts
CREATE OR REPLACE TABLE companies (
    company_id STRING PRIMARY KEY,
    company_name STRING NOT NULL,
    industry STRING,
    company_size STRING,
    annual_revenue NUMBER(15,2),
    employee_count INTEGER,
    website STRING,
    headquarters_location STRING,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Contacts
CREATE OR REPLACE TABLE contacts (
    contact_id STRING PRIMARY KEY,
    company_id STRING,
    first_name STRING NOT NULL,
    last_name STRING NOT NULL,
    email STRING,
    phone STRING,
    title STRING,
    department STRING,
    decision_maker_level STRING,
    champion_score INTEGER DEFAULT 0,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (company_id) REFERENCES companies(company_id)
);

-- Opportunities/Deals
CREATE OR REPLACE TABLE opportunities (
    opportunity_id STRING PRIMARY KEY,
    company_id STRING NOT NULL,
    owner_id STRING NOT NULL,
    opportunity_name STRING NOT NULL,
    stage STRING NOT NULL,
    amount NUMBER(15,2),
    probability NUMBER(3,2),
    close_date DATE,
    created_date DATE DEFAULT CURRENT_DATE(),
    last_activity_date DATE,
    source STRING,
    type STRING,
    description TEXT,
    competitor_info TEXT,
    next_steps TEXT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (owner_id) REFERENCES users(user_id)
);

-- =============================================
-- AI FEATURES TABLES (SNOWFLAKE NATIVE)
-- =============================================

-- Chat conversations using Snowflake Cortex
CREATE OR REPLACE TABLE chat_conversations (
    conversation_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    session_id STRING,
    message_text TEXT NOT NULL,
    response_text TEXT,
    cortex_model STRING DEFAULT 'llama2-70b-chat',
    intent_extracted STRING,
    entities_json VARIANT,
    confidence_score NUMBER(3,2),
    context_data VARIANT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Voice recordings processed with Snowflake Cortex
CREATE OR REPLACE TABLE voice_recordings (
    recording_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    contact_id STRING,
    stage_file_path STRING, -- Snowflake Stage path
    duration_seconds INTEGER,
    transcript_raw TEXT,
    transcript_processed TEXT,
    sentiment_score NUMBER(3,2), -- Using CORTEX.SENTIMENT()
    sentiment_label STRING,
    key_topics VARIANT, -- Extracted using CORTEX.EXTRACT_ANSWER()
    action_items VARIANT,
    meeting_summary TEXT, -- Using CORTEX.SUMMARIZE()
    crm_fields_extracted VARIANT, -- Auto-extracted CRM data
    processed_at TIMESTAMP_NTZ,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id)
);

-- AI-generated proposals using Snowflake Cortex
CREATE OR REPLACE TABLE proposals (
    proposal_id STRING PRIMARY KEY,
    opportunity_id STRING NOT NULL,
    user_id STRING NOT NULL,
    proposal_name STRING NOT NULL,
    template_id STRING,
    content_generated TEXT, -- Generated using CORTEX.COMPLETE()
    content_final TEXT,
    generation_prompt TEXT,
    cortex_model STRING DEFAULT 'llama2-70b-chat',
    status STRING DEFAULT 'draft',
    version INTEGER DEFAULT 1,
    total_value NUMBER(15,2),
    generated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    sent_at TIMESTAMP_NTZ,
    viewed_at TIMESTAMP_NTZ,
    approved_at TIMESTAMP_NTZ,
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Proposal templates for Cortex AI generation
CREATE OR REPLACE TABLE proposal_templates (
    template_id STRING PRIMARY KEY,
    template_name STRING NOT NULL,
    industry STRING,
    solution_type STRING,
    template_content TEXT,
    prompt_instructions TEXT, -- Instructions for Cortex AI
    variables VARIANT, -- Template variables
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Contract analysis using Snowflake Cortex
CREATE OR REPLACE TABLE contract_analysis (
    analysis_id STRING PRIMARY KEY,
    opportunity_id STRING,
    user_id STRING NOT NULL,
    contract_name STRING,
    stage_file_path STRING, -- Snowflake Stage path
    contract_text TEXT,
    risk_score INTEGER, -- Calculated using Snowpark ML
    compliance_score INTEGER,
    key_findings VARIANT, -- Extracted using CORTEX.EXTRACT_ANSWER()
    risk_factors VARIANT,
    suggested_modifications TEXT, -- Generated using CORTEX.COMPLETE()
    legal_review_required BOOLEAN DEFAULT FALSE,
    cortex_analysis VARIANT, -- Full Cortex AI analysis
    analyzed_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Deal risk assessments using Snowpark ML
CREATE OR REPLACE TABLE deal_risk_assessments (
    assessment_id STRING PRIMARY KEY,
    opportunity_id STRING NOT NULL,
    user_id STRING NOT NULL,
    risk_score INTEGER NOT NULL, -- Snowpark ML model output
    risk_level STRING NOT NULL,
    win_probability NUMBER(3,2),
    key_risk_factors VARIANT,
    recommendations TEXT, -- Generated using CORTEX.COMPLETE()
    confidence_score NUMBER(3,2),
    model_version STRING,
    feature_importance VARIANT,
    assessed_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Lead scoring using Snowpark ML
CREATE OR REPLACE TABLE lead_scores (
    score_id STRING PRIMARY KEY,
    company_id STRING,
    contact_id STRING,
    user_id STRING NOT NULL,
    lead_score INTEGER NOT NULL, -- Snowpark ML model output
    score_category STRING NOT NULL,
    scoring_factors VARIANT,
    model_version STRING,
    model_features VARIANT,
    scored_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Competitive intelligence using Snowflake Cortex
CREATE OR REPLACE TABLE competitive_intelligence (
    intelligence_id STRING PRIMARY KEY,
    competitor_name STRING NOT NULL,
    opportunity_id STRING,
    market_analysis VARIANT, -- Cortex AI analysis
    competitive_positioning TEXT, -- Generated using CORTEX.COMPLETE()
    strengths VARIANT,
    weaknesses VARIANT,
    pricing_intelligence VARIANT,
    win_loss_analysis VARIANT,
    battlecard_content TEXT, -- Auto-generated battlecard
    positioning_strategy TEXT,
    last_updated TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id)
);

-- Sales coaching using Snowflake Cortex
CREATE OR REPLACE TABLE coaching_recommendations (
    recommendation_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    coaching_area STRING NOT NULL,
    recommendation_text TEXT NOT NULL, -- Generated using CORTEX.COMPLETE()
    coaching_prompt TEXT,
    priority_level STRING DEFAULT 'medium',
    status STRING DEFAULT 'active',
    performance_data VARIANT,
    best_practices VARIANT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    completed_at TIMESTAMP_NTZ,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id)
);

-- =============================================
-- AUTOMATION & REPORTING TABLES
-- =============================================

-- Generated reports using Snowflake Cortex
CREATE OR REPLACE TABLE generated_reports (
    report_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    report_name STRING NOT NULL,
    report_type STRING NOT NULL,
    natural_language_query TEXT, -- Original user query
    generated_sql TEXT, -- SQL generated by Cortex AI
    parameters VARIANT,
    content_data VARIANT,
    content_narrative TEXT, -- Generated using CORTEX.COMPLETE()
    stage_file_path STRING, -- Report file in Snowflake Stage
    status STRING DEFAULT 'generating',
    generated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Activity tracking
CREATE OR REPLACE TABLE activities (
    activity_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    contact_id STRING,
    activity_type STRING NOT NULL,
    subject STRING,
    description TEXT,
    activity_date DATE,
    duration_minutes INTEGER,
    outcome STRING,
    next_steps TEXT,
    sentiment_score NUMBER(3,2), -- Using CORTEX.SENTIMENT()
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id)
);

-- Follow-up automation using Snowflake Tasks
CREATE OR REPLACE TABLE follow_up_automation (
    automation_id STRING PRIMARY KEY,
    opportunity_id STRING NOT NULL,
    user_id STRING NOT NULL,
    trigger_event STRING NOT NULL,
    follow_up_type STRING NOT NULL,
    scheduled_date TIMESTAMP_NTZ,
    content_template TEXT,
    generated_content TEXT, -- Using CORTEX.COMPLETE()
    status STRING DEFAULT 'scheduled',
    executed_at TIMESTAMP_NTZ,
    task_name STRING, -- Snowflake Task name
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- =============================================
-- SYSTEM TABLES
-- =============================================

-- Snowpark ML model performance tracking
CREATE OR REPLACE TABLE ml_model_performance (
    performance_id STRING PRIMARY KEY,
    model_name STRING NOT NULL,
    model_version STRING NOT NULL,
    model_type STRING NOT NULL, -- 'snowpark_ml' or 'cortex_ai'
    accuracy_score NUMBER(5,4),
    precision_score NUMBER(5,4),
    recall_score NUMBER(5,4),
    f1_score NUMBER(5,4),
    training_data_size INTEGER,
    feature_count INTEGER,
    training_date TIMESTAMP_NTZ,
    evaluation_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Cortex AI usage tracking
CREATE OR REPLACE TABLE cortex_usage_log (
    usage_id STRING PRIMARY KEY,
    user_id STRING,
    function_name STRING NOT NULL, -- COMPLETE, SENTIMENT, SUMMARIZE, etc.
    model_name STRING,
    input_text_length INTEGER,
    output_text_length INTEGER,
    processing_time_ms INTEGER,
    cost_credits NUMBER(10,6),
    success BOOLEAN DEFAULT TRUE,
    error_message STRING,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- System audit log
CREATE OR REPLACE TABLE audit_log (
    log_id STRING PRIMARY KEY,
    user_id STRING,
    action STRING NOT NULL,
    table_name STRING,
    record_id STRING,
    old_values VARIANT,
    new_values VARIANT,
    timestamp TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    session_id STRING,
    warehouse_name STRING
);
