-- Advanced Conversational AI Sales Assistant Database Schema
-- Nihilent x Snowflake Hackathon 2025

-- Create database and schema
CREATE DATABASE IF NOT EXISTS SALES_AI_DB;
USE DATABASE SALES_AI_DB;
CREATE SCHEMA IF NOT EXISTS SALES_AI_SCHEMA;
USE SCHEMA SALES_AI_SCHEMA;

-- =============================================
-- CORE TABLES
-- =============================================

-- Users and Authentication
CREATE OR REPLACE TABLE users (
    user_id STRING PRIMARY KEY,
    username STRING UNIQUE NOT NULL,
    email STRING UNIQUE NOT NULL,
    full_name STRING,
    role STRING DEFAULT 'sales_rep',
    department STRING,
    manager_id STRING,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Companies/Accounts
CREATE OR REPLACE TABLE companies (
    company_id STRING PRIMARY KEY,
    company_name STRING NOT NULL,
    industry STRING,
    company_size STRING,
    annual_revenue NUMBER(15,2),
    employee_count INTEGER,
    website STRING,
    headquarters_location STRING,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Contacts
CREATE OR REPLACE TABLE contacts (
    contact_id STRING PRIMARY KEY,
    company_id STRING,
    first_name STRING NOT NULL,
    last_name STRING NOT NULL,
    email STRING,
    phone STRING,
    title STRING,
    department STRING,
    decision_maker_level STRING,
    champion_score INTEGER DEFAULT 0,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (company_id) REFERENCES companies(company_id)
);

-- Opportunities/Deals
CREATE OR REPLACE TABLE opportunities (
    opportunity_id STRING PRIMARY KEY,
    company_id STRING NOT NULL,
    owner_id STRING NOT NULL,
    opportunity_name STRING NOT NULL,
    stage STRING NOT NULL,
    amount NUMBER(15,2),
    probability NUMBER(3,2),
    close_date DATE,
    created_date DATE DEFAULT CURRENT_DATE(),
    last_activity_date DATE,
    source STRING,
    type STRING,
    description TEXT,
    competitor_info TEXT,
    next_steps TEXT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (owner_id) REFERENCES users(user_id)
);

-- =============================================
-- AI FEATURES TABLES
-- =============================================

-- Chat Conversations
CREATE OR REPLACE TABLE chat_conversations (
    conversation_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    session_id STRING,
    message_text TEXT NOT NULL,
    response_text TEXT,
    intent STRING,
    entities VARIANT,
    confidence_score NUMBER(3,2),
    context_data VARIANT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Voice Processing Records
CREATE OR REPLACE TABLE voice_recordings (
    recording_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    contact_id STRING,
    file_path STRING,
    duration_seconds INTEGER,
    transcript TEXT,
    sentiment_score NUMBER(3,2),
    key_topics VARIANT,
    action_items VARIANT,
    meeting_summary TEXT,
    processed_at TIMESTAMP_NTZ,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id)
);

-- Generated Proposals
CREATE OR REPLACE TABLE proposals (
    proposal_id STRING PRIMARY KEY,
    opportunity_id STRING NOT NULL,
    user_id STRING NOT NULL,
    proposal_name STRING NOT NULL,
    template_used STRING,
    content TEXT,
    status STRING DEFAULT 'draft',
    version INTEGER DEFAULT 1,
    total_value NUMBER(15,2),
    generated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    sent_at TIMESTAMP_NTZ,
    viewed_at TIMESTAMP_NTZ,
    approved_at TIMESTAMP_NTZ,
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Contract Analysis
CREATE OR REPLACE TABLE contract_analysis (
    analysis_id STRING PRIMARY KEY,
    opportunity_id STRING,
    user_id STRING NOT NULL,
    contract_name STRING,
    file_path STRING,
    risk_score INTEGER,
    compliance_score INTEGER,
    key_findings VARIANT,
    suggested_modifications VARIANT,
    legal_review_required BOOLEAN DEFAULT FALSE,
    analyzed_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Deal Risk Assessments
CREATE OR REPLACE TABLE deal_risk_assessments (
    assessment_id STRING PRIMARY KEY,
    opportunity_id STRING NOT NULL,
    user_id STRING NOT NULL,
    risk_score INTEGER NOT NULL,
    risk_level STRING NOT NULL,
    win_probability NUMBER(3,2),
    key_risk_factors VARIANT,
    recommendations VARIANT,
    confidence_score NUMBER(3,2),
    model_version STRING,
    assessed_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Lead Scoring
CREATE OR REPLACE TABLE lead_scores (
    score_id STRING PRIMARY KEY,
    company_id STRING,
    contact_id STRING,
    user_id STRING NOT NULL,
    lead_score INTEGER NOT NULL,
    score_category STRING NOT NULL,
    factors VARIANT,
    model_version STRING,
    scored_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Competitive Intelligence
CREATE OR REPLACE TABLE competitive_intelligence (
    intelligence_id STRING PRIMARY KEY,
    competitor_name STRING NOT NULL,
    opportunity_id STRING,
    market_position VARIANT,
    strengths VARIANT,
    weaknesses VARIANT,
    pricing_info VARIANT,
    win_loss_data VARIANT,
    positioning_strategy TEXT,
    last_updated TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id)
);

-- Sales Coaching Recommendations
CREATE OR REPLACE TABLE coaching_recommendations (
    recommendation_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    coaching_area STRING NOT NULL,
    recommendation_text TEXT NOT NULL,
    priority_level STRING DEFAULT 'medium',
    status STRING DEFAULT 'active',
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    completed_at TIMESTAMP_NTZ,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id)
);

-- =============================================
-- REPORTING AND ANALYTICS TABLES
-- =============================================

-- Generated Reports
CREATE OR REPLACE TABLE generated_reports (
    report_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    report_name STRING NOT NULL,
    report_type STRING NOT NULL,
    parameters VARIANT,
    content TEXT,
    file_path STRING,
    status STRING DEFAULT 'generating',
    generated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Activity Tracking
CREATE OR REPLACE TABLE activities (
    activity_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    contact_id STRING,
    activity_type STRING NOT NULL,
    subject STRING,
    description TEXT,
    activity_date DATE,
    duration_minutes INTEGER,
    outcome STRING,
    next_steps TEXT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id)
);

-- Email Tracking
CREATE OR REPLACE TABLE email_tracking (
    email_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    contact_id STRING,
    subject STRING,
    sent_at TIMESTAMP_NTZ,
    opened_at TIMESTAMP_NTZ,
    clicked_at TIMESTAMP_NTZ,
    replied_at TIMESTAMP_NTZ,
    sentiment_score NUMBER(3,2),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id)
);

-- =============================================
-- SYSTEM TABLES
-- =============================================

-- Model Performance Tracking
CREATE OR REPLACE TABLE model_performance (
    performance_id STRING PRIMARY KEY,
    model_name STRING NOT NULL,
    model_version STRING NOT NULL,
    accuracy_score NUMBER(5,4),
    precision_score NUMBER(5,4),
    recall_score NUMBER(5,4),
    f1_score NUMBER(5,4),
    training_date TIMESTAMP_NTZ,
    evaluation_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- System Audit Log
CREATE OR REPLACE TABLE audit_log (
    log_id STRING PRIMARY KEY,
    user_id STRING,
    action STRING NOT NULL,
    table_name STRING,
    record_id STRING,
    old_values VARIANT,
    new_values VARIANT,
    timestamp TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    ip_address STRING,
    user_agent STRING
);

-- =============================================
-- VIEWS FOR ANALYTICS
-- =============================================

-- Sales Pipeline View
CREATE OR REPLACE VIEW v_sales_pipeline AS
SELECT 
    o.opportunity_id,
    o.opportunity_name,
    c.company_name,
    u.full_name as owner_name,
    o.stage,
    o.amount,
    o.probability,
    o.close_date,
    DATEDIFF('day', o.created_date, CURRENT_DATE()) as days_in_pipeline,
    CASE 
        WHEN o.close_date < CURRENT_DATE() AND o.stage NOT IN ('Closed Won', 'Closed Lost') 
        THEN 'Overdue'
        WHEN DATEDIFF('day', CURRENT_DATE(), o.close_date) <= 7 
        THEN 'Closing Soon'
        ELSE 'On Track'
    END as status
FROM opportunities o
JOIN companies c ON o.company_id = c.company_id
JOIN users u ON o.owner_id = u.user_id
WHERE o.stage NOT IN ('Closed Won', 'Closed Lost');

-- Deal Health View
CREATE OR REPLACE VIEW v_deal_health AS
SELECT 
    o.opportunity_id,
    o.opportunity_name,
    o.amount,
    o.stage,
    COALESCE(dra.risk_score, 50) as risk_score,
    COALESCE(dra.risk_level, 'Unknown') as risk_level,
    COALESCE(dra.win_probability, o.probability) as win_probability,
    COUNT(a.activity_id) as activity_count,
    MAX(a.activity_date) as last_activity_date
FROM opportunities o
LEFT JOIN deal_risk_assessments dra ON o.opportunity_id = dra.opportunity_id
LEFT JOIN activities a ON o.opportunity_id = a.opportunity_id
WHERE o.stage NOT IN ('Closed Won', 'Closed Lost')
GROUP BY o.opportunity_id, o.opportunity_name, o.amount, o.stage, dra.risk_score, dra.risk_level, dra.win_probability;
