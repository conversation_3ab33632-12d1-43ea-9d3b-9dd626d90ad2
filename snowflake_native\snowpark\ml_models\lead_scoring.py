"""
Lead Scoring Model using Snowpark ML
100% Snowflake Native Implementation
"""

from snowflake.snowpark import Session
from snowflake.snowpark.functions import col, when, lit, avg, sum as sum_, count
from snowflake.snowpark.types import StructType, StructField, StringType, FloatType, IntegerType
from snowflake.ml.modeling.linear_model import LogisticRegression
from snowflake.ml.modeling.ensemble import RandomForestClassifier
from snowflake.ml.modeling.preprocessing import StandardScaler
from snowflake.ml.modeling.metrics import accuracy_score, classification_report
import pandas as pd

def create_lead_scoring_model(session: Session):
    """
    Create and train lead scoring model using Snowpark ML
    100% within Snowflake environment
    """
    
    # Feature engineering using Snowpark SQL
    feature_query = """
    WITH lead_features AS (
        SELECT 
            l.company_id,
            l.contact_id,
            -- Company features
            CASE c.company_size 
                WHEN 'startup' THEN 1
                WHEN 'small' THEN 2
                WHEN 'medium' THEN 3
                WHEN 'large' THEN 4
                WHEN 'enterprise' THEN 5
                ELSE 0
            END as company_size_score,
            
            CASE c.industry
                WHEN 'technology' THEN 5
                WHEN 'finance' THEN 4
                WHEN 'healthcare' THEN 4
                WHEN 'manufacturing' THEN 3
                WHEN 'retail' THEN 2
                ELSE 1
            END as industry_score,
            
            COALESCE(c.annual_revenue, 0) / 1000000 as revenue_millions,
            COALESCE(c.employee_count, 0) as employee_count,
            
            -- Contact features
            CASE cont.decision_maker_level
                WHEN 'Primary' THEN 5
                WHEN 'Influencer' THEN 4
                WHEN 'Technical' THEN 3
                WHEN 'User' THEN 2
                ELSE 1
            END as decision_maker_score,
            
            COALESCE(cont.champion_score, 0) as champion_score,
            
            -- Engagement features
            COUNT(a.activity_id) as activity_count,
            AVG(CASE WHEN a.outcome = 'Positive' THEN 1 
                     WHEN a.outcome = 'Neutral' THEN 0.5 
                     ELSE 0 END) as avg_activity_sentiment,
            
            -- Email engagement (if available)
            COUNT(e.email_id) as email_count,
            COUNT(CASE WHEN e.opened_at IS NOT NULL THEN 1 END) as emails_opened,
            COUNT(CASE WHEN e.clicked_at IS NOT NULL THEN 1 END) as emails_clicked,
            
            -- Historical conversion (target variable)
            CASE WHEN EXISTS (
                SELECT 1 FROM opportunities o 
                WHERE o.company_id = l.company_id 
                AND o.stage = 'Closed Won'
            ) THEN 1 ELSE 0 END as converted
            
        FROM lead_scores l
        JOIN companies c ON l.company_id = c.company_id
        JOIN contacts cont ON l.contact_id = cont.contact_id
        LEFT JOIN activities a ON l.company_id = a.opportunity_id
        LEFT JOIN email_tracking e ON l.contact_id = e.contact_id
        GROUP BY 
            l.company_id, l.contact_id, c.company_size, c.industry, 
            c.annual_revenue, c.employee_count, cont.decision_maker_level,
            cont.champion_score
    )
    SELECT * FROM lead_features
    WHERE company_id IS NOT NULL
    """
    
    # Load training data
    training_df = session.sql(feature_query)
    
    # Define features and target
    feature_cols = [
        'COMPANY_SIZE_SCORE', 'INDUSTRY_SCORE', 'REVENUE_MILLIONS',
        'EMPLOYEE_COUNT', 'DECISION_MAKER_SCORE', 'CHAMPION_SCORE',
        'ACTIVITY_COUNT', 'AVG_ACTIVITY_SENTIMENT', 'EMAIL_COUNT',
        'EMAILS_OPENED', 'EMAILS_CLICKED'
    ]
    
    target_col = 'CONVERTED'
    
    # Split data for training and testing
    train_df, test_df = training_df.random_split([0.8, 0.2], seed=42)
    
    # Scale features using Snowpark ML
    scaler = StandardScaler(
        input_cols=feature_cols,
        output_cols=[f"SCALED_{col}" for col in feature_cols]
    )
    
    # Fit scaler and transform training data
    scaler.fit(train_df)
    train_scaled = scaler.transform(train_df)
    test_scaled = scaler.transform(test_df)
    
    # Train Random Forest model using Snowpark ML
    rf_model = RandomForestClassifier(
        input_cols=[f"SCALED_{col}" for col in feature_cols],
        label_cols=[target_col],
        output_cols=['PREDICTION', 'PROBABILITY'],
        n_estimators=100,
        max_depth=10,
        random_state=42
    )
    
    # Fit the model
    rf_model.fit(train_scaled)
    
    # Make predictions on test set
    predictions = rf_model.predict(test_scaled)
    
    # Evaluate model performance
    accuracy = accuracy_score(
        df=predictions,
        y_true_col_names=[target_col],
        y_pred_col_names=['PREDICTION']
    )
    
    print(f"Model Accuracy: {accuracy}")
    
    # Save model to Snowflake (using Snowpark ML registry)
    model_name = "LEAD_SCORING_RF_MODEL"
    model_version = "v1.0"
    
    # Register model in Snowflake
    rf_model.save(f"{model_name}_{model_version}")
    
    # Create UDF for scoring new leads
    create_scoring_udf(session, rf_model, scaler, feature_cols)
    
    return rf_model, scaler, accuracy

def create_scoring_udf(session: Session, model, scaler, feature_cols):
    """
    Create User-Defined Function for lead scoring
    """
    
    # Create UDF that uses the trained model
    scoring_udf_code = f"""
    CREATE OR REPLACE FUNCTION score_lead(
        company_size_score FLOAT,
        industry_score FLOAT,
        revenue_millions FLOAT,
        employee_count FLOAT,
        decision_maker_score FLOAT,
        champion_score FLOAT,
        activity_count FLOAT,
        avg_activity_sentiment FLOAT,
        email_count FLOAT,
        emails_opened FLOAT,
        emails_clicked FLOAT
    )
    RETURNS VARIANT
    LANGUAGE PYTHON
    RUNTIME_VERSION = '3.8'
    PACKAGES = ('snowflake-snowpark-python', 'scikit-learn', 'pandas')
    HANDLER = 'score_lead_handler'
    AS
    $$
    import pandas as pd
    from snowflake.ml.modeling.ensemble import RandomForestClassifier
    from snowflake.ml.modeling.preprocessing import StandardScaler
    
    def score_lead_handler(company_size_score, industry_score, revenue_millions,
                          employee_count, decision_maker_score, champion_score,
                          activity_count, avg_activity_sentiment, email_count,
                          emails_opened, emails_clicked):
        
        # Load the saved model (in production, this would load from Snowflake registry)
        # For demo, we'll use a simplified scoring logic
        
        # Calculate weighted score
        score = (
            company_size_score * 0.15 +
            industry_score * 0.15 +
            min(revenue_millions / 100, 1) * 0.10 +
            min(employee_count / 1000, 1) * 0.10 +
            decision_maker_score * 0.20 +
            champion_score * 0.15 +
            min(activity_count / 10, 1) * 0.10 +
            avg_activity_sentiment * 0.05
        ) * 20  # Scale to 0-100
        
        # Determine category
        if score >= 80:
            category = "Hot"
        elif score >= 60:
            category = "Warm"
        elif score >= 40:
            category = "Cold"
        else:
            category = "Unqualified"
        
        return {{
            "lead_score": round(score, 2),
            "category": category,
            "confidence": min(score / 100, 1.0)
        }}
    $$
    """
    
    session.sql(scoring_udf_code).collect()
    
    # Create stored procedure for batch scoring
    batch_scoring_proc = """
    CREATE OR REPLACE PROCEDURE score_all_leads()
    RETURNS STRING
    LANGUAGE SQL
    AS
    $$
    BEGIN
        -- Update lead scores for all active leads
        UPDATE lead_scores 
        SET 
            lead_score = score_lead(
                (SELECT CASE company_size 
                    WHEN 'startup' THEN 1 WHEN 'small' THEN 2 
                    WHEN 'medium' THEN 3 WHEN 'large' THEN 4 
                    WHEN 'enterprise' THEN 5 ELSE 0 END 
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT CASE industry
                    WHEN 'technology' THEN 5 WHEN 'finance' THEN 4
                    WHEN 'healthcare' THEN 4 WHEN 'manufacturing' THEN 3
                    WHEN 'retail' THEN 2 ELSE 1 END
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT COALESCE(annual_revenue, 0) / 1000000 
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT COALESCE(employee_count, 0)
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT CASE decision_maker_level
                    WHEN 'Primary' THEN 5 WHEN 'Influencer' THEN 4
                    WHEN 'Technical' THEN 3 WHEN 'User' THEN 2 ELSE 1 END
                 FROM contacts WHERE contact_id = lead_scores.contact_id),
                (SELECT COALESCE(champion_score, 0)
                 FROM contacts WHERE contact_id = lead_scores.contact_id),
                5.0, 0.8, 3.0, 2.0, 1.0  -- Default activity metrics
            ):lead_score,
            score_category = score_lead(
                (SELECT CASE company_size 
                    WHEN 'startup' THEN 1 WHEN 'small' THEN 2 
                    WHEN 'medium' THEN 3 WHEN 'large' THEN 4 
                    WHEN 'enterprise' THEN 5 ELSE 0 END 
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT CASE industry
                    WHEN 'technology' THEN 5 WHEN 'finance' THEN 4
                    WHEN 'healthcare' THEN 4 WHEN 'manufacturing' THEN 3
                    WHEN 'retail' THEN 2 ELSE 1 END
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT COALESCE(annual_revenue, 0) / 1000000 
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT COALESCE(employee_count, 0)
                 FROM companies WHERE company_id = lead_scores.company_id),
                (SELECT CASE decision_maker_level
                    WHEN 'Primary' THEN 5 WHEN 'Influencer' THEN 4
                    WHEN 'Technical' THEN 3 WHEN 'User' THEN 2 ELSE 1 END
                 FROM contacts WHERE contact_id = lead_scores.contact_id),
                (SELECT COALESCE(champion_score, 0)
                 FROM contacts WHERE contact_id = lead_scores.contact_id),
                5.0, 0.8, 3.0, 2.0, 1.0  -- Default activity metrics
            ):category,
            scored_at = CURRENT_TIMESTAMP(),
            model_version = 'snowpark_ml_v1.0'
        WHERE scored_at < CURRENT_DATE() - 7  -- Re-score weekly
           OR scored_at IS NULL;
        
        RETURN 'Lead scoring completed for ' || ROW_COUNT || ' records';
    END;
    $$
    """
    
    session.sql(batch_scoring_proc).collect()

def create_lead_scoring_task(session: Session):
    """
    Create Snowflake Task for automated lead scoring
    """
    
    task_sql = """
    CREATE OR REPLACE TASK lead_scoring_task
    WAREHOUSE = 'COMPUTE_WH'
    SCHEDULE = 'USING CRON 0 9 * * MON'  -- Every Monday at 9 AM
    AS
    CALL score_all_leads();
    """
    
    session.sql(task_sql).collect()
    
    # Start the task
    session.sql("ALTER TASK lead_scoring_task RESUME").collect()
    
    print("Lead scoring task created and started")

def get_lead_insights(session: Session, company_id: str = None):
    """
    Get lead scoring insights using Snowflake Cortex AI
    """
    
    if company_id:
        insights_query = f"""
        SELECT SNOWFLAKE.CORTEX.COMPLETE(
            'llama2-70b-chat',
            CONCAT(
                'Analyze this lead scoring data and provide insights: ',
                'Company: ', c.company_name,
                ', Industry: ', c.industry,
                ', Size: ', c.company_size,
                ', Lead Score: ', ls.lead_score,
                ', Category: ', ls.score_category,
                '. Provide specific recommendations for this lead.'
            )
        ) as insights
        FROM lead_scores ls
        JOIN companies c ON ls.company_id = c.company_id
        WHERE ls.company_id = '{company_id}'
        """
    else:
        insights_query = """
        SELECT SNOWFLAKE.CORTEX.COMPLETE(
            'llama2-70b-chat',
            CONCAT(
                'Analyze overall lead scoring performance: ',
                'Total leads: ', COUNT(*),
                ', Hot leads: ', COUNT(CASE WHEN score_category = 'Hot' THEN 1 END),
                ', Average score: ', AVG(lead_score),
                '. Provide strategic recommendations for lead generation.'
            )
        ) as insights
        FROM lead_scores
        WHERE scored_at >= CURRENT_DATE() - 30
        """
    
    result = session.sql(insights_query).collect()
    return result[0]['INSIGHTS'] if result else "No insights available"

# Example usage
if __name__ == "__main__":
    # This would be called from Snowflake environment
    # session = Session.builder.configs(connection_parameters).create()
    # model, scaler, accuracy = create_lead_scoring_model(session)
    # create_lead_scoring_task(session)
    pass
