"""
Snowpark ML models for the Advanced Conversational AI Sales Assistant
"""
from typing import Dict, List, Any, Optional
import pandas as pd
from snowflake.snowpark import Session
from snowflake.snowpark.functions import col, when, lit, avg, sum as sum_
from snowflake.snowpark.types import StructType, StructField, StringType, FloatType, IntegerType
import joblib
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, mean_squared_error


class LeadScoringModel:
    """ML model for lead scoring and qualification"""
    
    def __init__(self, session: Session):
        self.session = session
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
    
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for lead scoring"""
        # Feature engineering
        df['company_size_score'] = df['company_size'].map({
            'startup': 1, 'small': 2, 'medium': 3, 'large': 4, 'enterprise': 5
        })
        
        df['industry_score'] = df['industry'].map({
            'technology': 5, 'finance': 4, 'healthcare': 4, 
            'manufacturing': 3, 'retail': 2, 'other': 1
        })
        
        df['engagement_score'] = (
            df['email_opens'] * 0.1 + 
            df['email_clicks'] * 0.3 + 
            df['website_visits'] * 0.2 + 
            df['demo_requests'] * 0.4
        )
        
        # Select features for training
        feature_columns = [
            'company_size_score', 'industry_score', 'engagement_score',
            'budget_range', 'decision_timeline', 'pain_point_severity'
        ]
        
        return df[feature_columns]
    
    def train(self, training_data: pd.DataFrame) -> Dict[str, float]:
        """Train the lead scoring model"""
        features = self.prepare_features(training_data)
        target = training_data['converted']
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, target, test_size=0.2, random_state=42
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate
        y_pred = self.model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        self.is_trained = True
        
        return {
            'accuracy': accuracy,
            'feature_importance': dict(zip(features.columns, self.model.feature_importances_))
        }
    
    def predict_lead_score(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict lead score for a single lead"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        # Convert to DataFrame
        df = pd.DataFrame([lead_data])
        features = self.prepare_features(df)
        
        # Scale and predict
        features_scaled = self.scaler.transform(features)
        probability = self.model.predict_proba(features_scaled)[0][1]
        prediction = self.model.predict(features_scaled)[0]
        
        # Determine score category
        if probability >= 0.8:
            category = "Hot"
        elif probability >= 0.6:
            category = "Warm"
        elif probability >= 0.4:
            category = "Cold"
        else:
            category = "Unqualified"
        
        return {
            'lead_score': float(probability),
            'category': category,
            'prediction': bool(prediction),
            'confidence': float(max(probability, 1 - probability))
        }


class DealRiskAssessmentModel:
    """ML model for assessing deal risk and probability"""
    
    def __init__(self, session: Session):
        self.session = session
        self.model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
    
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for deal risk assessment"""
        # Calculate deal age
        df['deal_age_days'] = (pd.Timestamp.now() - pd.to_datetime(df['created_date'])).dt.days
        
        # Engagement metrics
        df['stakeholder_engagement'] = df['stakeholder_count'] * df['avg_meeting_frequency']
        df['communication_score'] = df['email_count'] + df['call_count'] * 2
        
        # Competitive pressure
        df['competitive_pressure'] = df['competitor_count'] * df['competitive_threat_level']
        
        # Budget alignment
        df['budget_fit'] = df['deal_value'] / df['customer_budget']
        
        feature_columns = [
            'deal_age_days', 'deal_value', 'stakeholder_engagement',
            'communication_score', 'competitive_pressure', 'budget_fit',
            'decision_timeline', 'champion_strength', 'technical_fit_score'
        ]
        
        return df[feature_columns]
    
    def train(self, training_data: pd.DataFrame) -> Dict[str, float]:
        """Train the deal risk assessment model"""
        features = self.prepare_features(training_data)
        target = training_data['win_probability']
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, target, test_size=0.2, random_state=42
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate
        y_pred = self.model.predict(X_test_scaled)
        mse = mean_squared_error(y_test, y_pred)
        
        self.is_trained = True
        
        return {
            'mse': mse,
            'rmse': np.sqrt(mse),
            'feature_importance': dict(zip(features.columns, self.model.feature_importances_))
        }
    
    def assess_deal_risk(self, deal_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for a specific deal"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        # Convert to DataFrame
        df = pd.DataFrame([deal_data])
        features = self.prepare_features(df)
        
        # Scale and predict
        features_scaled = self.scaler.transform(features)
        win_probability = self.model.predict(features_scaled)[0]
        
        # Determine risk level
        if win_probability >= 0.7:
            risk_level = "Low"
            recommendations = ["Continue current strategy", "Prepare for closing"]
        elif win_probability >= 0.4:
            risk_level = "Medium"
            recommendations = ["Increase stakeholder engagement", "Address competitive threats"]
        else:
            risk_level = "High"
            recommendations = ["Urgent intervention needed", "Re-evaluate deal strategy", "Consider walking away"]
        
        return {
            'win_probability': float(win_probability),
            'risk_level': risk_level,
            'recommendations': recommendations,
            'confidence_score': float(min(win_probability, 1 - win_probability) * 2)
        }


class PricingOptimizationModel:
    """ML model for dynamic pricing optimization"""
    
    def __init__(self, session: Session):
        self.session = session
        self.model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
    
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for pricing optimization"""
        # Market factors
        df['market_demand_score'] = df['industry_growth_rate'] * df['market_size']
        df['competitive_intensity'] = df['competitor_count'] / df['market_size']
        
        # Customer factors
        df['customer_value_score'] = df['customer_revenue'] * df['customer_growth_rate']
        df['urgency_factor'] = 1 / (df['decision_timeline'] + 1)
        
        # Product factors
        df['solution_complexity'] = df['feature_count'] * df['customization_level']
        
        feature_columns = [
            'market_demand_score', 'competitive_intensity', 'customer_value_score',
            'urgency_factor', 'solution_complexity', 'customer_budget',
            'historical_discount_rate', 'deal_size_category'
        ]
        
        return df[feature_columns]
    
    def train(self, training_data: pd.DataFrame) -> Dict[str, float]:
        """Train the pricing optimization model"""
        features = self.prepare_features(training_data)
        target = training_data['optimal_price']
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, target, test_size=0.2, random_state=42
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate
        y_pred = self.model.predict(X_test_scaled)
        mse = mean_squared_error(y_test, y_pred)
        
        self.is_trained = True
        
        return {
            'mse': mse,
            'rmse': np.sqrt(mse),
            'feature_importance': dict(zip(features.columns, self.model.feature_importances_))
        }
    
    def optimize_pricing(self, deal_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate optimal pricing recommendation"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        # Convert to DataFrame
        df = pd.DataFrame([deal_context])
        features = self.prepare_features(df)
        
        # Scale and predict
        features_scaled = self.scaler.transform(features)
        optimal_price = self.model.predict(features_scaled)[0]
        
        # Calculate price ranges
        base_price = deal_context.get('base_price', optimal_price)
        discount_range = {
            'min_discount': max(0, (base_price - optimal_price) / base_price * 0.8),
            'max_discount': min(0.3, (base_price - optimal_price) / base_price * 1.2)
        }
        
        return {
            'optimal_price': float(optimal_price),
            'recommended_discount': float((base_price - optimal_price) / base_price),
            'price_range': {
                'min_price': float(optimal_price * 0.9),
                'max_price': float(optimal_price * 1.1)
            },
            'discount_range': discount_range,
            'confidence_score': 0.85  # Placeholder - would be calculated based on model uncertainty
        }
