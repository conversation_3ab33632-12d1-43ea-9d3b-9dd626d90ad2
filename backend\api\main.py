"""
Main FastAPI application for the Advanced Conversational AI Sales Assistant
"""
from fastapi import FastAP<PERSON>, HTTPException, Depends, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, List, Any, Optional
import uvicorn
import logging
from contextlib import asynccontextmanager

from config import settings, features
from api.routes import (
    chat, crm, reports, proposals, contracts, 
    voice, coaching, intelligence, risk_assessment
)
from api.middleware import setup_middleware
from api.dependencies import get_snowflake_session, get_current_user
from snowpark_ml.models import LeadScoringModel, DealRiskAssessmentModel, PricingOptimizationModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Global model instances
models = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Starting Advanced Conversational AI Sales Assistant")
    
    # Initialize ML models
    try:
        session = get_snowflake_session()
        models['lead_scoring'] = LeadScoringModel(session)
        models['deal_risk'] = DealRiskAssessmentModel(session)
        models['pricing'] = PricingOptimizationModel(session)
        logger.info("ML models initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize ML models: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application")


# Create FastAPI app
app = FastAPI(
    title=settings.app.app_name,
    version=settings.app.version,
    description="Advanced Conversational AI Sales Assistant with unique features",
    lifespan=lifespan
)

# Setup middleware
setup_middleware(app)

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Advanced Conversational AI Sales Assistant",
        "version": settings.app.version,
        "features": {
            "voice_processing": features.VOICE_PROCESSING,
            "proposal_generation": features.PROPOSAL_GENERATION,
            "contract_analysis": features.CONTRACT_ANALYSIS,
            "competitive_intelligence": features.COMPETITIVE_INTELLIGENCE,
            "deal_risk_assessment": features.DEAL_RISK_ASSESSMENT,
            "automated_followup": features.AUTOMATED_FOLLOWUP,
            "report_generation": features.REPORT_GENERATION,
            "sales_coaching": features.SALES_COACHING
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test Snowflake connection
        session = get_snowflake_session()
        session.sql("SELECT 1").collect()
        
        return {
            "status": "healthy",
            "database": "connected",
            "models": {
                "lead_scoring": "lead_scoring" in models,
                "deal_risk": "deal_risk" in models,
                "pricing": "pricing" in models
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


@app.post("/api/v1/chat")
async def chat_endpoint(
    message: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """Main chat endpoint for conversational AI"""
    try:
        # Process the chat message
        response = await chat.process_message(
            message=message.get("message", ""),
            user_id=current_user.get("user_id"),
            context=message.get("context", {}),
            session=get_snowflake_session()
        )
        
        return {
            "response": response,
            "user_id": current_user.get("user_id"),
            "timestamp": message.get("timestamp")
        }
    except Exception as e:
        logger.error(f"Chat processing failed: {e}")
        raise HTTPException(status_code=500, detail="Chat processing failed")


@app.post("/api/v1/voice/process")
async def process_voice(
    audio_file: UploadFile = File(...),
    current_user: Dict = Depends(get_current_user)
):
    """Process voice input and convert to CRM data"""
    if not features.VOICE_PROCESSING:
        raise HTTPException(status_code=404, detail="Voice processing feature disabled")
    
    try:
        result = await voice.process_audio_file(
            audio_file=audio_file,
            user_id=current_user.get("user_id"),
            session=get_snowflake_session()
        )
        
        return result
    except Exception as e:
        logger.error(f"Voice processing failed: {e}")
        raise HTTPException(status_code=500, detail="Voice processing failed")


@app.post("/api/v1/reports/generate")
async def generate_report(
    report_request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """Generate intelligent sales reports"""
    if not features.REPORT_GENERATION:
        raise HTTPException(status_code=404, detail="Report generation feature disabled")
    
    try:
        # Start report generation in background
        background_tasks.add_task(
            reports.generate_report_async,
            report_request=report_request,
            user_id=current_user.get("user_id"),
            session=get_snowflake_session()
        )
        
        return {
            "message": "Report generation started",
            "report_id": report_request.get("report_id"),
            "status": "processing"
        }
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        raise HTTPException(status_code=500, detail="Report generation failed")


@app.post("/api/v1/proposals/generate")
async def generate_proposal(
    proposal_request: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """Generate AI-powered proposals"""
    if not features.PROPOSAL_GENERATION:
        raise HTTPException(status_code=404, detail="Proposal generation feature disabled")
    
    try:
        result = await proposals.generate_proposal(
            request=proposal_request,
            user_id=current_user.get("user_id"),
            session=get_snowflake_session()
        )
        
        return result
    except Exception as e:
        logger.error(f"Proposal generation failed: {e}")
        raise HTTPException(status_code=500, detail="Proposal generation failed")


@app.post("/api/v1/contracts/analyze")
async def analyze_contract(
    contract_file: UploadFile = File(...),
    current_user: Dict = Depends(get_current_user)
):
    """Analyze and suggest contract modifications"""
    if not features.CONTRACT_ANALYSIS:
        raise HTTPException(status_code=404, detail="Contract analysis feature disabled")
    
    try:
        result = await contracts.analyze_contract(
            contract_file=contract_file,
            user_id=current_user.get("user_id"),
            session=get_snowflake_session()
        )
        
        return result
    except Exception as e:
        logger.error(f"Contract analysis failed: {e}")
        raise HTTPException(status_code=500, detail="Contract analysis failed")


@app.get("/api/v1/intelligence/competitive")
async def get_competitive_intelligence(
    competitor: Optional[str] = None,
    industry: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """Get competitive intelligence and market insights"""
    if not features.COMPETITIVE_INTELLIGENCE:
        raise HTTPException(status_code=404, detail="Competitive intelligence feature disabled")
    
    try:
        result = await intelligence.get_competitive_analysis(
            competitor=competitor,
            industry=industry,
            user_id=current_user.get("user_id"),
            session=get_snowflake_session()
        )
        
        return result
    except Exception as e:
        logger.error(f"Competitive intelligence failed: {e}")
        raise HTTPException(status_code=500, detail="Competitive intelligence failed")


@app.post("/api/v1/deals/assess-risk")
async def assess_deal_risk(
    deal_data: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """Assess deal risk and provide recommendations"""
    if not features.DEAL_RISK_ASSESSMENT:
        raise HTTPException(status_code=404, detail="Deal risk assessment feature disabled")
    
    try:
        # Use the ML model for risk assessment
        model = models.get('deal_risk')
        if not model:
            raise HTTPException(status_code=503, detail="Risk assessment model not available")
        
        result = model.assess_deal_risk(deal_data)
        
        # Store assessment in database
        await risk_assessment.store_assessment(
            deal_id=deal_data.get("deal_id"),
            assessment=result,
            user_id=current_user.get("user_id"),
            session=get_snowflake_session()
        )
        
        return result
    except Exception as e:
        logger.error(f"Deal risk assessment failed: {e}")
        raise HTTPException(status_code=500, detail="Deal risk assessment failed")


@app.get("/api/v1/coaching/suggestions")
async def get_coaching_suggestions(
    deal_id: Optional[str] = None,
    stage: Optional[str] = None,
    current_user: Dict = Depends(get_current_user)
):
    """Get AI-powered sales coaching suggestions"""
    if not features.SALES_COACHING:
        raise HTTPException(status_code=404, detail="Sales coaching feature disabled")
    
    try:
        result = await coaching.get_coaching_suggestions(
            deal_id=deal_id,
            stage=stage,
            user_id=current_user.get("user_id"),
            session=get_snowflake_session()
        )
        
        return result
    except Exception as e:
        logger.error(f"Sales coaching failed: {e}")
        raise HTTPException(status_code=500, detail="Sales coaching failed")


# Include route modules
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])
app.include_router(crm.router, prefix="/api/v1/crm", tags=["crm"])
app.include_router(reports.router, prefix="/api/v1/reports", tags=["reports"])
app.include_router(proposals.router, prefix="/api/v1/proposals", tags=["proposals"])
app.include_router(contracts.router, prefix="/api/v1/contracts", tags=["contracts"])
app.include_router(voice.router, prefix="/api/v1/voice", tags=["voice"])
app.include_router(coaching.router, prefix="/api/v1/coaching", tags=["coaching"])
app.include_router(intelligence.router, prefix="/api/v1/intelligence", tags=["intelligence"])
app.include_router(risk_assessment.router, prefix="/api/v1/risk", tags=["risk"])


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.debug,
        log_level="info"
    )
