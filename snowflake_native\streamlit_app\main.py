"""
Advanced Conversational AI Sales Assistant
100% Snowflake Native Implementation using Streamlit in Snowflake (SiS)
Nihilent x Snowflake Hackathon 2025
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from snowflake.snowpark.context import get_active_session
from snowflake.snowpark.functions import col, call_udf, lit
import json
from datetime import datetime, timedelta

# Get Snowflake session (native to Streamlit in Snowflake)
session = get_active_session()

# Page configuration
st.set_page_config(
    page_title="AI Sales Assistant - Snowflake Native",
    page_icon="❄️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for Snowflake branding
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #29B5E8;
        text-align: center;
        margin-bottom: 2rem;
    }
    .snowflake-card {
        background: linear-gradient(135deg, #29B5E8 0%, #1E88E5 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .feature-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #29B5E8;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
        border: 1px solid #e0e0e0;
    }
    .sidebar-logo {
        text-align: center;
        padding: 1rem;
        font-size: 1.5rem;
        font-weight: bold;
        color: #29B5E8;
    }
    .cortex-badge {
        background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
        color: white;
        padding: 0.2rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = True  # Simplified for demo
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'user_data' not in st.session_state:
    st.session_state.user_data = {"username": "demo_user", "role": "sales_rep"}

def main_app():
    """Main application interface"""
    
    # Header
    st.markdown('<div class="main-header">❄️ AI Sales Assistant</div>', unsafe_allow_html=True)
    st.markdown('<div style="text-align: center; color: #666; margin-bottom: 2rem;">100% Snowflake Native • Powered by Cortex AI</div>', unsafe_allow_html=True)
    
    # Sidebar navigation
    with st.sidebar:
        st.markdown('<div class="sidebar-logo">❄️ Snowflake Native</div>', unsafe_allow_html=True)
        st.markdown('<div class="cortex-badge">Cortex AI Powered</div>', unsafe_allow_html=True)
        
        # User info
        st.markdown(f"**Welcome, {st.session_state.user_data.get('username', 'User')}!**")
        
        # Navigation
        page = st.selectbox(
            "Choose Feature",
            [
                "🏠 Dashboard",
                "💬 Cortex Chat Assistant", 
                "🎤 Voice-to-CRM (Cortex)",
                "📝 AI Proposal Writer (Cortex)",
                "📋 Smart Contract Analysis",
                "⚠️ Deal Risk Assessment",
                "🔍 Competitive Intelligence",
                "📊 Natural Language Reports",
                "🎯 AI Sales Coaching"
            ]
        )
        
        # Snowflake info
        st.markdown("---")
        st.markdown("**Snowflake Features Used:**")
        st.markdown("• Cortex AI Functions")
        st.markdown("• Streamlit in Snowflake")
        st.markdown("• Snowpark for Python")
        st.markdown("• Native Tables & Stages")
        st.markdown("• Tasks & Streams")
    
    # Route to selected page
    if page == "🏠 Dashboard":
        show_dashboard()
    elif page == "💬 Cortex Chat Assistant":
        show_cortex_chat()
    elif page == "🎤 Voice-to-CRM (Cortex)":
        show_voice_processing()
    elif page == "📝 AI Proposal Writer (Cortex)":
        show_proposal_writer()
    elif page == "📋 Smart Contract Analysis":
        show_contract_analysis()
    elif page == "⚠️ Deal Risk Assessment":
        show_risk_assessment()
    elif page == "🔍 Competitive Intelligence":
        show_competitive_intelligence()
    elif page == "📊 Natural Language Reports":
        show_nl_reports()
    elif page == "🎯 AI Sales Coaching":
        show_sales_coaching()

def show_dashboard():
    """Dashboard with Snowflake native data"""
    st.title("📊 Sales Dashboard")
    st.markdown("Real-time insights powered by Snowflake Cortex AI")
    
    # Fetch dashboard data using Snowpark
    try:
        # Pipeline metrics
        pipeline_df = session.sql("""
            SELECT 
                COUNT(*) as total_deals,
                SUM(amount) as total_value,
                AVG(probability) as avg_probability,
                COUNT(CASE WHEN stage = 'Negotiation' THEN 1 END) as closing_soon
            FROM opportunities 
            WHERE stage NOT IN ('Closed Won', 'Closed Lost')
        """).to_pandas()
        
        # Display key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_value = pipeline_df['TOTAL_VALUE'].iloc[0] if not pipeline_df.empty else 0
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #29B5E8;">${total_value:,.0f}</h3>
                <p>Pipeline Value</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            total_deals = pipeline_df['TOTAL_DEALS'].iloc[0] if not pipeline_df.empty else 0
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #FF6B6B;">{total_deals}</h3>
                <p>Active Deals</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            avg_prob = pipeline_df['AVG_PROBABILITY'].iloc[0] if not pipeline_df.empty else 0
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #4ECDC4;">{avg_prob:.0%}</h3>
                <p>Avg Win Rate</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            closing_soon = pipeline_df['CLOSING_SOON'].iloc[0] if not pipeline_df.empty else 0
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #FFA726;">{closing_soon}</h3>
                <p>Closing Soon</p>
            </div>
            """, unsafe_allow_html=True)
        
        st.markdown("---")
        
        # Charts
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📈 Pipeline by Stage")
            
            # Get pipeline data
            stage_df = session.sql("""
                SELECT stage, COUNT(*) as count, SUM(amount) as value
                FROM opportunities 
                WHERE stage NOT IN ('Closed Won', 'Closed Lost')
                GROUP BY stage
                ORDER BY 
                    CASE stage
                        WHEN 'Prospecting' THEN 1
                        WHEN 'Qualification' THEN 2
                        WHEN 'Proposal' THEN 3
                        WHEN 'Negotiation' THEN 4
                        ELSE 5
                    END
            """).to_pandas()
            
            if not stage_df.empty:
                fig = px.funnel(stage_df, x='COUNT', y='STAGE', title="Deals by Stage")
                fig.update_traces(marker_color=['#29B5E8', '#4ECDC4', '#FFA726', '#FF6B6B'])
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("💰 Revenue Trend")
            
            # Mock revenue data (in real implementation, this would come from Snowflake)
            dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
            revenue_data = pd.DataFrame({
                'Date': dates,
                'Revenue': [200000 + i*5000 + (i%7)*10000 for i in range(len(dates))]
            })
            
            fig = px.line(revenue_data, x='Date', y='Revenue', title="Daily Revenue")
            fig.update_traces(line_color='#29B5E8')
            st.plotly_chart(fig, use_container_width=True)
        
        # AI Insights using Cortex
        st.subheader("🤖 Cortex AI Insights")
        
        # Generate insights using Cortex AI
        if st.button("Generate AI Insights", type="primary"):
            with st.spinner("Cortex AI analyzing your pipeline..."):
                try:
                    insights_query = """
                    SELECT SNOWFLAKE.CORTEX.COMPLETE(
                        'llama2-70b-chat',
                        CONCAT(
                            'Analyze this sales pipeline data and provide 3 key insights: ',
                            'Total deals: ', (SELECT COUNT(*) FROM opportunities WHERE stage NOT IN ('Closed Won', 'Closed Lost')),
                            ', Total value: $', (SELECT SUM(amount) FROM opportunities WHERE stage NOT IN ('Closed Won', 'Closed Lost')),
                            ', Average probability: ', (SELECT AVG(probability) FROM opportunities WHERE stage NOT IN ('Closed Won', 'Closed Lost')),
                            '. Provide actionable recommendations for sales improvement.'
                        )
                    ) as insights
                    """
                    
                    insights_result = session.sql(insights_query).collect()
                    if insights_result:
                        insights_text = insights_result[0]['INSIGHTS']
                        
                        st.markdown(f"""
                        <div class="snowflake-card">
                            <h4>🧠 Cortex AI Analysis</h4>
                            <p>{insights_text}</p>
                        </div>
                        """, unsafe_allow_html=True)
                except Exception as e:
                    st.error(f"Error generating insights: {str(e)}")
        
        # Recent activities
        st.subheader("🔔 Recent AI Activities")
        
        activities = [
            "🎯 Lead 'TechCorp Inc.' scored 85% using Snowpark ML model",
            "⚠️ Deal 'Enterprise Solution' flagged as high risk by Cortex AI",
            "📝 Proposal generated for 'Healthcare Systems' using Cortex COMPLETE()",
            "🏆 Competitive analysis updated using Cortex intelligence",
            "📞 Voice call processed and CRM auto-updated via Cortex functions"
        ]
        
        for activity in activities:
            st.markdown(f"""
            <div class="feature-card">
                {activity}
            </div>
            """, unsafe_allow_html=True)
            
    except Exception as e:
        st.error(f"Error loading dashboard data: {str(e)}")
        st.info("Make sure the database schema is properly set up.")

def show_cortex_chat():
    """Cortex AI Chat Assistant"""
    st.title("💬 Cortex AI Chat Assistant")
    st.markdown("Powered by Snowflake Cortex AI Functions")
    
    # Chat interface
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.subheader("Chat with Cortex AI")
        
        # Display chat history
        for i, chat in enumerate(st.session_state.chat_history):
            if chat['type'] == 'user':
                st.markdown(f"""
                <div style="background-color: #e3f2fd; padding: 10px; border-radius: 10px; margin: 5px 0;">
                    <strong>You:</strong> {chat['content']}
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div style="background-color: #f3e5f5; padding: 10px; border-radius: 10px; margin: 5px 0;">
                    <strong>Cortex AI:</strong> {chat['content']}
                </div>
                """, unsafe_allow_html=True)
        
        # Chat input
        user_input = st.text_input("Ask Cortex AI about your sales data:", key="chat_input")
        
        col_send, col_clear = st.columns([1, 1])
        
        with col_send:
            if st.button("Send to Cortex", use_container_width=True, type="primary") and user_input:
                # Add user message
                st.session_state.chat_history.append({
                    'type': 'user',
                    'content': user_input,
                    'timestamp': datetime.now()
                })
                
                # Get Cortex AI response
                with st.spinner("Cortex AI thinking..."):
                    try:
                        cortex_query = f"""
                        SELECT SNOWFLAKE.CORTEX.COMPLETE(
                            'llama2-70b-chat',
                            'You are an AI sales assistant with access to Snowflake sales data. Answer this question: {user_input}. Provide specific, actionable insights.'
                        ) as response
                        """
                        
                        result = session.sql(cortex_query).collect()
                        if result:
                            ai_response = result[0]['RESPONSE']
                            
                            st.session_state.chat_history.append({
                                'type': 'bot',
                                'content': ai_response,
                                'timestamp': datetime.now()
                            })
                        else:
                            st.session_state.chat_history.append({
                                'type': 'bot',
                                'content': "I'm sorry, I couldn't process that request.",
                                'timestamp': datetime.now()
                            })
                    except Exception as e:
                        st.session_state.chat_history.append({
                            'type': 'bot',
                            'content': f"Error: {str(e)}",
                            'timestamp': datetime.now()
                        })
                
                st.rerun()
        
        with col_clear:
            if st.button("Clear Chat", use_container_width=True):
                st.session_state.chat_history = []
                st.rerun()
    
    with col2:
        st.subheader("Quick Actions")
        
        quick_actions = [
            "Show my pipeline",
            "Analyze deal risks",
            "Generate Q1 report",
            "Find hot leads",
            "Competitive analysis",
            "Coaching suggestions"
        ]
        
        for action in quick_actions:
            if st.button(action, use_container_width=True):
                # Process quick action
                st.session_state.chat_history.append({
                    'type': 'user',
                    'content': action,
                    'timestamp': datetime.now()
                })
                
                # Mock AI response for demo
                responses = {
                    "Show my pipeline": "Your current pipeline has 23 active deals worth $2.4M total. 5 deals are in negotiation stage.",
                    "Analyze deal risks": "I've identified 3 high-risk deals that need immediate attention. TechCorp deal shows 45% win probability.",
                    "Generate Q1 report": "Generating Q1 sales report with Cortex AI... Report will include pipeline analysis and forecasts.",
                    "Find hot leads": "Found 8 hot leads with 80%+ scores. Top lead: Manufacturing Corp with $600K potential.",
                    "Competitive analysis": "Competitor A is active in 4 of your deals. Recommended positioning strategy generated.",
                    "Coaching suggestions": "Based on your recent activities, focus on stakeholder engagement and objection handling."
                }
                
                st.session_state.chat_history.append({
                    'type': 'bot',
                    'content': responses.get(action, "Processing your request..."),
                    'timestamp': datetime.now()
                })
                
                st.rerun()

def show_voice_processing():
    """Voice-to-CRM using Cortex AI"""
    st.title("🎤 Voice-to-CRM Processing")
    st.markdown("Upload audio files to Snowflake Stages and process with Cortex AI")
    
    # Feature overview
    with st.expander("❄️ Snowflake Native Features", expanded=True):
        st.markdown("""
        - **File Storage**: Snowflake Stages for audio files
        - **AI Processing**: Cortex AI for transcription and analysis
        - **Sentiment Analysis**: `SNOWFLAKE.CORTEX.SENTIMENT()` function
        - **Data Extraction**: `SNOWFLAKE.CORTEX.EXTRACT_ANSWER()` for CRM fields
        - **Auto-Population**: Snowpark procedures for CRM updates
        """)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📁 Upload to Snowflake Stage")
        
        # File upload simulation (in real SiS, this would upload to Snowflake Stage)
        uploaded_file = st.file_uploader(
            "Choose an audio file (will be stored in Snowflake Stage)",
            type=['wav', 'mp3', 'm4a'],
            help="File will be uploaded to Snowflake Stage for processing"
        )
        
        if uploaded_file is not None:
            st.success(f"File uploaded to Snowflake Stage: @VOICE_STAGE/{uploaded_file.name}")
            
            # Processing options
            st.subheader("⚙️ Cortex AI Processing")
            
            col_opt1, col_opt2 = st.columns(2)
            
            with col_opt1:
                extract_sentiment = st.checkbox("Sentiment Analysis (Cortex)", value=True)
                generate_summary = st.checkbox("Meeting Summary (Cortex)", value=True)
                extract_crm = st.checkbox("CRM Field Extraction", value=True)
            
            with col_opt2:
                auto_update = st.checkbox("Auto CRM Update", value=True)
                create_tasks = st.checkbox("Create Follow-up Tasks", value=True)
                competitive_intel = st.checkbox("Competitive Mentions", value=True)
            
            # Process button
            if st.button("🚀 Process with Cortex AI", use_container_width=True, type="primary"):
                with st.spinner("Processing with Snowflake Cortex AI..."):
                    # Simulate processing results
                    show_voice_processing_results(uploaded_file.name)
    
    with col2:
        st.subheader("📊 Recent Processed Calls")
        
        # Get recent voice recordings from Snowflake
        try:
            recent_calls_df = session.sql("""
                SELECT 
                    recording_id,
                    stage_file_path,
                    sentiment_label,
                    duration_seconds,
                    processed_at
                FROM voice_recordings 
                ORDER BY processed_at DESC 
                LIMIT 5
            """).to_pandas()
            
            if not recent_calls_df.empty:
                for _, call in recent_calls_df.iterrows():
                    st.markdown(f"""
                    <div class="feature-card">
                        <strong>File:</strong> {call['STAGE_FILE_PATH']}<br>
                        <strong>Duration:</strong> {call['DURATION_SECONDS']}s<br>
                        <strong>Sentiment:</strong> {call['SENTIMENT_LABEL']}<br>
                        <strong>Processed:</strong> {call['PROCESSED_AT']}
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.info("No processed calls found. Upload an audio file to get started.")
                
        except Exception as e:
            st.info("Upload and process audio files to see recent calls here.")

def show_voice_processing_results(filename):
    """Show voice processing results using Cortex AI"""
    st.success("🎉 Cortex AI processing completed!")
    
    # Mock transcript for demo
    mock_transcript = """
    Sales Rep: Good morning! Thank you for taking the time to speak with me about our AI platform.
    
    Customer: Good morning. Yes, I'm very interested in learning how your solution can help our operations.
    
    Sales Rep: Excellent! Based on our previous conversation, I understand you're looking to improve efficiency. Our platform has helped similar companies reduce costs by up to 30%.
    
    Customer: That sounds promising. What about implementation time? We can't afford long downtime.
    
    Sales Rep: Great question. Our typical implementation takes 4-6 weeks with minimal disruption.
    
    Customer: Interesting. We're working with a budget of around $500K for this initiative.
    
    Sales Rep: That budget works well with our enterprise package. I'd like to schedule a demo next week.
    
    Customer: That would be great. Let's set that up.
    """
    
    # Tabs for different results
    tab1, tab2, tab3, tab4 = st.tabs(["📝 Cortex Transcript", "😊 Sentiment Analysis", "📋 AI Summary", "🔄 CRM Updates"])
    
    with tab1:
        st.subheader("Speech-to-Text via Cortex AI")
        st.text_area("Transcript", mock_transcript, height=300)
        
        st.markdown("""
        <div class="snowflake-card">
            <strong>Snowflake Features Used:</strong><br>
            • File stored in Snowflake Stage<br>
            • Cortex AI for speech-to-text processing<br>
            • Native table storage for transcript
        </div>
        """, unsafe_allow_html=True)
    
    with tab2:
        st.subheader("Cortex Sentiment Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("Overall Sentiment", "Positive (0.75)", "↑ 15%")
            st.metric("Customer Engagement", "High", "↑ 8%")
            st.metric("Interest Level", "Strong", "↑ 12%")
        
        with col2:
            # Sentiment timeline
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=[0, 5, 10, 15, 20, 25, 30],
                y=[0.6, 0.7, 0.8, 0.75, 0.85, 0.9, 0.85],
                mode='lines+markers',
                name='Sentiment Score',
                line=dict(color='#29B5E8', width=3)
            ))
            fig.update_layout(
                title="Sentiment Throughout Call (Cortex AI)",
                xaxis_title="Time (minutes)",
                yaxis_title="Sentiment Score",
                height=300
            )
            st.plotly_chart(fig, use_container_width=True)
        
        st.markdown("""
        <div class="snowflake-card">
            <strong>Cortex Function Used:</strong><br>
            <code>SNOWFLAKE.CORTEX.SENTIMENT(transcript)</code>
        </div>
        """, unsafe_allow_html=True)
    
    with tab3:
        st.subheader("AI-Generated Summary")
        
        summary_data = {
            "Meeting Type": "Sales Discovery Call",
            "Participants": "Sales Rep, Customer (TechCorp)",
            "Duration": "30 minutes",
            "Key Points": [
                "Customer interested in AI platform for operations",
                "Budget confirmed at $500K range",
                "Implementation timeline is critical concern",
                "Demo requested for next week"
            ],
            "Action Items": [
                "Schedule product demo for next week",
                "Prepare implementation timeline document",
                "Send pricing proposal",
                "Follow up with technical specifications"
            ],
            "Next Steps": "Demo scheduled, proposal to follow"
        }
        
        for key, value in summary_data.items():
            if isinstance(value, list):
                st.markdown(f"**{key}:**")
                for item in value:
                    st.markdown(f"- {item}")
            else:
                st.markdown(f"**{key}:** {value}")
        
        st.markdown("""
        <div class="snowflake-card">
            <strong>Cortex Functions Used:</strong><br>
            <code>SNOWFLAKE.CORTEX.SUMMARIZE(transcript)</code><br>
            <code>SNOWFLAKE.CORTEX.EXTRACT_ANSWER(transcript, question)</code>
        </div>
        """, unsafe_allow_html=True)
    
    with tab4:
        st.subheader("Automatic CRM Updates")
        
        st.success("✅ CRM automatically updated using Snowpark procedures")
        
        crm_updates = {
            "Contact Information": "Updated phone and email",
            "Deal Stage": "Advanced from 'Prospecting' to 'Qualification'",
            "Budget": "Added budget range: $500K",
            "Timeline": "Implementation needed within 6 weeks",
            "Next Activity": "Product demo scheduled",
            "Opportunity Value": "Updated to $500K",
            "Sentiment Score": "Positive (0.75)",
            "Decision Timeline": "Q2 2024"
        }
        
        for field, update in crm_updates.items():
            st.markdown(f"**{field}:** {update}")
        
        st.markdown("""
        <div class="snowflake-card">
            <strong>Snowflake Native Process:</strong><br>
            1. Audio file stored in Snowflake Stage<br>
            2. Cortex AI processes transcript<br>
            3. Snowpark procedure extracts CRM fields<br>
            4. Native tables updated automatically<br>
            5. Snowflake Tasks trigger follow-up workflows
        </div>
        """, unsafe_allow_html=True)

# Additional page functions would be implemented similarly...
# Each using 100% Snowflake native capabilities

if __name__ == "__main__":
    main_app()
