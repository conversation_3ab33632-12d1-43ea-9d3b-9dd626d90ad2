# Setup Guide - Advanced Conversational AI Sales Assistant
## Nihilent x Snowflake Hackathon 2025

This guide will help you set up and run the Advanced Conversational AI Sales Assistant with all its unique features.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit UI  │    │   FastAPI       │    │   Snowflake     │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐            ┌─────▼─────┐         ┌──────▼──────┐
    │ Voice   │            │ Snowpark  │         │ Cortex AI   │
    │ Process │            │ ML Models │         │ Functions   │
    └─────────┘            └───────────┘         └─────────────┘
```

## 📋 Prerequisites

### 1. Snowflake Account
- Snowflake account with ACCOUNTADMIN privileges
- Warehouse with appropriate compute resources
- Snowflake Cortex enabled (for AI features)

### 2. Development Environment
- Python 3.8 or higher
- Git
- Code editor (VS Code recommended)

### 3. API Keys
- OpenAI API key (for enhanced AI features)
- Optional: CRM API keys (Salesforce, HubSpot)

## 🚀 Quick Start

### Step 1: Clone and Setup Project

```bash
# Clone the repository
git clone <repository-url>
cd Nihilent_X_Snowflake_2

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

### Step 2: Install Dependencies

```bash
# Install backend dependencies
cd backend
pip install -r requirements.txt

# Install frontend dependencies
cd ../frontend
pip install -r requirements.txt
```

### Step 3: Configure Environment

Create `.env` file in the backend directory:

```env
# Snowflake Configuration
SNOWFLAKE_ACCOUNT=your_account.region
SNOWFLAKE_USER=your_username
SNOWFLAKE_PASSWORD=your_password
SNOWFLAKE_WAREHOUSE=COMPUTE_WH
SNOWFLAKE_DATABASE=SALES_AI_DB
SNOWFLAKE_SCHEMA=SALES_AI_SCHEMA
SNOWFLAKE_ROLE=ACCOUNTADMIN

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=2000

# Application Configuration
SECRET_KEY=your_secret_key_here
DEBUG=True
HOST=0.0.0.0
PORT=8000

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# CRM Integration (optional)
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret
HUBSPOT_API_KEY=your_hubspot_api_key
```

### Step 4: Setup Snowflake Database

```bash
# Connect to Snowflake and run the schema creation
# Use Snowflake Web UI or SnowSQL

# 1. Create database and schema
snowsql -c your_connection -f database/schemas/sales_ai_schema.sql

# 2. Load sample data
snowsql -c your_connection -f database/sample_data.sql
```

### Step 5: Start the Application

```bash
# Terminal 1: Start Backend API
cd backend
python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Start Frontend UI
cd frontend
streamlit run app.py --server.port 8501
```

### Step 6: Access the Application

- **Frontend UI**: http://localhost:8501
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🌟 Feature Configuration

### Voice Processing Setup

1. Install additional audio dependencies:
```bash
pip install pyaudio soundfile
```

2. Configure microphone permissions in your browser

3. Test voice processing:
   - Navigate to "Voice Processing" in the UI
   - Upload a sample audio file
   - Verify transcription and CRM integration

### AI Model Training

1. Train the ML models with your data:
```python
# In Python console or script
from backend.snowpark_ml.models import LeadScoringModel, DealRiskAssessmentModel
from backend.api.dependencies import get_snowflake_session

session = get_snowflake_session()

# Train lead scoring model
lead_model = LeadScoringModel(session)
# Add your training data here
# lead_model.train(training_data)

# Train deal risk model
risk_model = DealRiskAssessmentModel(session)
# Add your training data here
# risk_model.train(training_data)
```

### CRM Integration

1. Configure your CRM credentials in `.env`
2. Test CRM connection:
```bash
# Test Salesforce connection
python -c "from backend.integrations.salesforce import test_connection; test_connection()"

# Test HubSpot connection
python -c "from backend.integrations.hubspot import test_connection; test_connection()"
```

## 🎯 Demo Scenarios

### 1. Voice-to-CRM Demo
1. Navigate to "Voice Processing"
2. Upload sample audio file: `demo_files/customer_call.wav`
3. Watch automatic CRM population
4. Review generated meeting summary and action items

### 2. AI Proposal Generation Demo
1. Go to "Proposal Writer"
2. Select "TechCorp Inc." as customer
3. Choose "Enterprise Software Solution"
4. Add custom requirements
5. Generate and review AI-created proposal

### 3. Deal Risk Assessment Demo
1. Open "Deal Risk Assessment"
2. Select an existing deal or create new one
3. Input deal parameters
4. View AI risk analysis and recommendations
5. Export risk report

### 4. Intelligent Reporting Demo
1. Access "Report Generation"
2. Use natural language: "Show me Q1 pipeline by sales rep"
3. Review auto-generated visualizations
4. Export report in multiple formats

### 5. Competitive Intelligence Demo
1. Navigate to "Competitive Intelligence"
2. Select a competitor
3. View market positioning analysis
4. Get strategic recommendations

## 🔧 Troubleshooting

### Common Issues

1. **Snowflake Connection Error**
   ```
   Error: Failed to connect to Snowflake
   Solution: Verify credentials in .env file and network connectivity
   ```

2. **Missing Dependencies**
   ```
   Error: ModuleNotFoundError
   Solution: Ensure all requirements.txt files are installed
   ```

3. **Voice Processing Not Working**
   ```
   Error: Audio processing failed
   Solution: Install pyaudio and check microphone permissions
   ```

4. **API Rate Limits**
   ```
   Error: OpenAI API rate limit exceeded
   Solution: Check API usage and upgrade plan if needed
   ```

### Performance Optimization

1. **Snowflake Warehouse Sizing**
   - Use X-Small for development
   - Scale to Small/Medium for production

2. **Caching Configuration**
   - Enable Redis for better performance
   - Configure appropriate TTL values

3. **Model Optimization**
   - Retrain models with more data
   - Tune hyperparameters for better accuracy

## 📊 Monitoring and Logging

### Application Monitoring
- Check logs in `backend/logs/`
- Monitor API performance at `/metrics`
- Use Snowflake query history for database monitoring

### Model Performance
- Track model accuracy in `model_performance` table
- Monitor prediction confidence scores
- Set up alerts for model drift

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**
   ```bash
   # Set production environment variables
   export DEBUG=False
   export HOST=0.0.0.0
   export PORT=8000
   ```

2. **Database Migration**
   ```sql
   -- Run production schema setup
   -- Update connection parameters for production Snowflake account
   ```

3. **Security Configuration**
   - Use strong secret keys
   - Enable HTTPS
   - Configure proper authentication
   - Set up firewall rules

4. **Scaling Considerations**
   - Use load balancer for multiple API instances
   - Configure Snowflake auto-scaling
   - Implement connection pooling

## 📞 Support

For technical support during the hackathon:
- Check the troubleshooting section above
- Review Snowflake documentation
- Contact the development team

## 🏆 Hackathon Submission

This solution demonstrates:
- ✅ Conversational AI for sales automation
- ✅ Snowflake Cortex integration
- ✅ Snowpark for Python ML models
- ✅ Unique differentiating features
- ✅ Real-world business value
- ✅ Scalable architecture
- ✅ Comprehensive demo scenarios

**Key Differentiators:**
1. Voice-to-CRM automation
2. AI-powered proposal generation
3. Smart contract analysis
4. Predictive deal risk assessment
5. Real-time competitive intelligence
6. Automated follow-up orchestration
7. Intelligent report generation
8. Personalized sales coaching

---
*Built with ❤️ for Nihilent x Snowflake Hackathon 2025*
