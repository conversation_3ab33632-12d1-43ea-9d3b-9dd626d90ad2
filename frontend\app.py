"""
Streamlit UI for Advanced Conversational AI Sales Assistant
<PERSON><PERSON><PERSON> x Snowflake Hackathon 2025
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from streamlit_option_menu import option_menu
from streamlit_chat import message
import requests
import json
from datetime import datetime, timedelta
import base64
import io
from typing import Dict, List, Any

# Page configuration
st.set_page_config(
    page_title="AI Sales Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .feature-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #1f77b4;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    .chat-container {
        max-height: 400px;
        overflow-y: auto;
        padding: 1rem;
        border: 1px solid #ddd;
        border-radius: 10px;
        background-color: #fafafa;
    }
    .sidebar-logo {
        text-align: center;
        padding: 1rem;
        font-size: 1.5rem;
        font-weight: bold;
        color: #1f77b4;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'user_data' not in st.session_state:
    st.session_state.user_data = {}

# API Configuration
API_BASE_URL = "http://localhost:8000/api/v1"

class APIClient:
    """API client for backend communication"""

    def __init__(self, base_url: str, token: str = None):
        self.base_url = base_url
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"

    def post(self, endpoint: str, data: Dict) -> Dict:
        """Make POST request to API"""
        try:
            response = requests.post(
                f"{self.base_url}{endpoint}",
                json=data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            st.error(f"API Error: {e}")
            return {"error": str(e)}

    def get(self, endpoint: str, params: Dict = None) -> Dict:
        """Make GET request to API"""
        try:
            response = requests.get(
                f"{self.base_url}{endpoint}",
                params=params,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            st.error(f"API Error: {e}")
            return {"error": str(e)}

# Initialize API client
api_client = APIClient(API_BASE_URL, st.session_state.get('token'))

def login_page():
    """Login page"""
    st.markdown('<div class="main-header">🤖 AI Sales Assistant</div>', unsafe_allow_html=True)
    st.markdown('<div class="main-header" style="font-size: 1.2rem;">Nihilent x Snowflake Hackathon 2025</div>', unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("### Login to Access AI Sales Assistant")

        with st.form("login_form"):
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            submit_button = st.form_submit_button("Login", use_container_width=True)

            if submit_button:
                # Mock authentication - replace with real authentication
                if username and password:
                    st.session_state.authenticated = True
                    st.session_state.user_data = {
                        "username": username,
                        "role": "sales_rep",
                        "token": "mock_jwt_token"
                    }
                    st.success("Login successful!")
                    st.rerun()
                else:
                    st.error("Please enter both username and password")

        # Demo credentials
        st.info("Demo Credentials: username: demo, password: demo")

def main_app():
    """Main application interface"""

    # Sidebar
    with st.sidebar:
        st.markdown('<div class="sidebar-logo">🤖 AI Sales Assistant</div>', unsafe_allow_html=True)

        # User info
        st.markdown(f"**Welcome, {st.session_state.user_data.get('username', 'User')}!**")

        # Navigation menu
        selected = option_menu(
            menu_title="Navigation",
            options=[
                "Dashboard",
                "Chat Assistant",
                "Voice Processing",
                "Report Generation",
                "Proposal Writer",
                "Contract Analysis",
                "Deal Risk Assessment",
                "Competitive Intelligence",
                "Sales Coaching"
            ],
            icons=[
                "speedometer2",
                "chat-dots",
                "mic",
                "file-earmark-text",
                "file-earmark-plus",
                "file-earmark-check",
                "exclamation-triangle",
                "graph-up",
                "lightbulb"
            ],
            menu_icon="cast",
            default_index=0,
        )

        # Logout button
        if st.button("Logout", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.user_data = {}
            st.session_state.chat_history = []
            st.rerun()

    # Main content area
    if selected == "Dashboard":
        show_dashboard()
    elif selected == "Chat Assistant":
        show_chat_assistant()
    elif selected == "Voice Processing":
        show_voice_processing()
    elif selected == "Report Generation":
        show_report_generation()
    elif selected == "Proposal Writer":
        show_proposal_writer()
    elif selected == "Contract Analysis":
        show_contract_analysis()
    elif selected == "Deal Risk Assessment":
        show_deal_risk_assessment()
    elif selected == "Competitive Intelligence":
        show_competitive_intelligence()
    elif selected == "Sales Coaching":
        show_sales_coaching()

def show_dashboard():
    """Dashboard page"""
    st.title("📊 Sales Dashboard")

    # Key metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #1f77b4;">$2.4M</h3>
            <p>Pipeline Value</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #ff7f0e;">23</h3>
            <p>Active Deals</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #2ca02c;">67%</h3>
            <p>Win Rate</p>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown("""
        <div class="metric-card">
            <h3 style="color: #d62728;">5</h3>
            <p>At-Risk Deals</p>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("---")

    # Charts
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📈 Sales Pipeline by Stage")

        # Sample data
        pipeline_data = pd.DataFrame({
            'Stage': ['Prospecting', 'Qualification', 'Proposal', 'Negotiation', 'Closed Won'],
            'Count': [15, 12, 8, 5, 3],
            'Value': [500000, 800000, 600000, 400000, 300000]
        })

        fig = px.funnel(pipeline_data, x='Count', y='Stage', title="Deal Count by Stage")
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        st.subheader("💰 Revenue Trend")

        # Sample data
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='M')
        revenue_data = pd.DataFrame({
            'Month': dates,
            'Revenue': [200000, 250000, 300000, 280000, 350000, 400000,
                       380000, 420000, 450000, 480000, 500000, 520000]
        })

        fig = px.line(revenue_data, x='Month', y='Revenue', title="Monthly Revenue")
        st.plotly_chart(fig, use_container_width=True)

    # Recent activities
    st.subheader("🔔 Recent AI Insights")

    insights = [
        "🎯 Lead 'TechCorp Inc.' scored 85% - High conversion probability",
        "⚠️ Deal 'Enterprise Solution' flagged as high risk - Requires attention",
        "📝 Proposal generated for 'Healthcare Systems Ltd.' - Ready for review",
        "🏆 Competitive analysis updated - New positioning strategy available",
        "📞 Voice call with 'Manufacturing Co.' processed - CRM updated automatically"
    ]

    for insight in insights:
        st.markdown(f"""
        <div class="feature-card">
            {insight}
        </div>
        """, unsafe_allow_html=True)

def show_chat_assistant():
    """Chat assistant page"""
    st.title("💬 Conversational AI Assistant")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("Chat with your AI Sales Assistant")

        # Chat container
        chat_container = st.container()

        with chat_container:
            # Display chat history
            for i, chat in enumerate(st.session_state.chat_history):
                if chat['type'] == 'user':
                    message(chat['content'], is_user=True, key=f"user_{i}")
                else:
                    message(chat['content'], key=f"bot_{i}")

        # Chat input
        user_input = st.text_input("Type your message here...", key="chat_input")

        col_send, col_clear = st.columns([1, 1])

        with col_send:
            if st.button("Send", use_container_width=True) and user_input:
                # Add user message to history
                st.session_state.chat_history.append({
                    'type': 'user',
                    'content': user_input,
                    'timestamp': datetime.now()
                })

                # Mock AI response (replace with actual API call)
                ai_response = process_chat_message(user_input)

                # Add AI response to history
                st.session_state.chat_history.append({
                    'type': 'bot',
                    'content': ai_response,
                    'timestamp': datetime.now()
                })

                st.rerun()

        with col_clear:
            if st.button("Clear Chat", use_container_width=True):
                st.session_state.chat_history = []
                st.rerun()

    with col2:
        st.subheader("Quick Actions")

        quick_actions = [
            "Show my pipeline",
            "Generate Q4 report",
            "Create proposal for ABC Corp",
            "Analyze competitor pricing",
            "Risk assessment for Deal #123",
            "Schedule follow-up reminder"
        ]

        for action in quick_actions:
            if st.button(action, use_container_width=True):
                # Add quick action to chat
                st.session_state.chat_history.append({
                    'type': 'user',
                    'content': action,
                    'timestamp': datetime.now()
                })

                ai_response = process_chat_message(action)

                st.session_state.chat_history.append({
                    'type': 'bot',
                    'content': ai_response,
                    'timestamp': datetime.now()
                })

                st.rerun()

def process_chat_message(message: str) -> str:
    """Process chat message and return AI response"""
    # Mock responses - replace with actual API calls
    message_lower = message.lower()

    if "pipeline" in message_lower:
        return "📊 Here's your current pipeline: 23 active deals worth $2.4M total. 5 deals in negotiation stage need attention."
    elif "report" in message_lower:
        return "📈 I'll generate your Q4 report. It will include pipeline analysis, win/loss rates, and revenue projections. Expected completion in 2 minutes."
    elif "proposal" in message_lower:
        return "📝 I'll create a customized proposal for ABC Corp based on your recent meetings and their requirements. Would you like me to include pricing options?"
    elif "competitor" in message_lower:
        return "🔍 Analyzing competitor data... Based on recent market intelligence, here are key positioning strategies against your main competitors."
    elif "risk" in message_lower:
        return "⚠️ Deal #123 shows medium risk (45% win probability). Recommendations: Increase stakeholder engagement, address pricing concerns, schedule executive meeting."
    else:
        return f"I understand you're asking about: '{message}'. Let me help you with that. What specific information do you need?"

# Import page modules
from pages.voice_processing import show_voice_processing
from pages.proposal_writer import show_proposal_writer
from pages.deal_risk_assessment import show_deal_risk_assessment

def show_report_generation():
    """Report generation page"""
    st.title("📊 Intelligent Report Generation")
    st.markdown("Generate comprehensive sales reports with AI-powered insights and visualizations.")

    # Feature overview
    with st.expander("🌟 Report Generation Features", expanded=False):
        st.markdown("""
        - **Natural Language Queries**: Ask for reports in plain English
        - **Auto-Visualization**: Intelligent chart and graph generation
        - **Custom Templates**: Industry-specific report formats
        - **Real-time Data**: Live connection to CRM and sales data
        - **Export Options**: PDF, Excel, PowerPoint formats
        - **Scheduled Reports**: Automated report delivery
        """)

    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("🎯 Report Configuration")

        # Report type selection
        report_types = [
            "Sales Pipeline Report",
            "Revenue Analysis",
            "Team Performance Report",
            "Customer Analysis",
            "Competitive Analysis",
            "Forecast Report",
            "Custom Report"
        ]

        selected_report = st.selectbox("Report Type", report_types)

        # Time period
        col_time1, col_time2 = st.columns(2)
        with col_time1:
            time_period = st.selectbox(
                "Time Period",
                ["Last 7 days", "Last 30 days", "Last Quarter", "Last Year", "Custom Range"]
            )

        with col_time2:
            if time_period == "Custom Range":
                start_date = st.date_input("Start Date")
                end_date = st.date_input("End Date")

        # Filters
        st.subheader("🔍 Filters")

        col_filter1, col_filter2 = st.columns(2)

        with col_filter1:
            sales_rep = st.multiselect(
                "Sales Representatives",
                ["All", "John Smith", "Sarah Johnson", "Mike Chen", "Lisa Brown"]
            )

            regions = st.multiselect(
                "Regions",
                ["All", "North America", "Europe", "Asia Pacific", "Latin America"]
            )

        with col_filter2:
            industries = st.multiselect(
                "Industries",
                ["All", "Technology", "Healthcare", "Manufacturing", "Financial Services"]
            )

            deal_stages = st.multiselect(
                "Deal Stages",
                ["All", "Prospecting", "Qualification", "Proposal", "Negotiation", "Closed Won"]
            )

        # Natural language query
        st.subheader("💬 Natural Language Query")
        query = st.text_area(
            "Describe the report you want",
            placeholder="e.g., 'Show me the top performing sales reps this quarter with their win rates and average deal sizes'",
            height=100
        )

        # Generate report button
        if st.button("🚀 Generate Report", use_container_width=True, type="primary"):
            with st.spinner("Generating intelligent report..."):
                import time
                time.sleep(3)

                st.success("✅ Report generated successfully!")

                # Mock report data
                show_sample_report(selected_report)

    with col2:
        st.subheader("📋 Recent Reports")

        recent_reports = [
            {"name": "Q4 Pipeline Report", "date": "2024-01-15", "type": "Pipeline"},
            {"name": "Team Performance", "date": "2024-01-14", "type": "Performance"},
            {"name": "Revenue Analysis", "date": "2024-01-13", "type": "Revenue"}
        ]

        for report in recent_reports:
            st.markdown(f"""
            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;">
                <strong>{report['name']}</strong><br>
                📅 {report['date']}<br>
                📊 {report['type']}
            </div>
            """, unsafe_allow_html=True)

def show_sample_report(report_type):
    """Show a sample generated report"""
    st.markdown("---")
    st.subheader(f"📊 {report_type}")

    # Sample data and visualizations
    if report_type == "Sales Pipeline Report":
        # Pipeline metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Pipeline", "$2.4M", "↑ 15%")
        with col2:
            st.metric("Active Deals", "23", "↑ 3")
        with col3:
            st.metric("Avg Deal Size", "$104K", "↑ 8%")
        with col4:
            st.metric("Win Rate", "67%", "↑ 5%")

        # Pipeline chart
        pipeline_data = pd.DataFrame({
            'Stage': ['Prospecting', 'Qualification', 'Proposal', 'Negotiation'],
            'Count': [15, 12, 8, 5],
            'Value': [500000, 800000, 600000, 400000]
        })

        fig = px.bar(pipeline_data, x='Stage', y='Value', title="Pipeline Value by Stage")
        st.plotly_chart(fig, use_container_width=True)

def show_contract_analysis():
    """Contract analysis page"""
    st.title("📋 Smart Contract Analysis")
    st.markdown("AI-powered contract analysis with modification suggestions and risk assessment.")

    st.info("🚧 Contract Analysis feature - Upload contracts for AI-powered analysis and suggestions")

    uploaded_file = st.file_uploader("Upload Contract", type=['pdf', 'docx'])

    if uploaded_file:
        st.success("Contract uploaded successfully!")

        # Mock analysis results
        col1, col2 = st.columns(2)

        with col1:
            st.subheader("📊 Analysis Results")
            st.metric("Risk Score", "Medium", "3 issues found")
            st.metric("Compliance Score", "92%", "↑ 5%")

        with col2:
            st.subheader("🔍 Key Findings")
            st.markdown("- Payment terms need clarification")
            st.markdown("- Liability clause requires review")
            st.markdown("- Termination conditions are standard")

def show_competitive_intelligence():
    """Competitive intelligence page"""
    st.title("🔍 Competitive Intelligence")
    st.markdown("Real-time competitive analysis and market positioning insights.")

    st.info("🚧 Competitive Intelligence feature - Get real-time competitor analysis and positioning strategies")

    # Competitor selection
    competitors = ["Competitor A", "Competitor B", "Competitor C"]
    selected_competitor = st.selectbox("Select Competitor", competitors)

    if selected_competitor:
        col1, col2 = st.columns(2)

        with col1:
            st.subheader("📊 Market Position")
            st.metric("Market Share", "15%", "↓ 2%")
            st.metric("Win Rate vs Us", "35%", "↓ 5%")

        with col2:
            st.subheader("💡 Positioning Strategy")
            st.markdown("- Emphasize superior integration capabilities")
            st.markdown("- Highlight faster implementation timeline")
            st.markdown("- Focus on better ROI and cost savings")

def show_sales_coaching():
    """Sales coaching page"""
    st.title("🎯 AI Sales Coaching")
    st.markdown("Personalized coaching recommendations based on deal analysis and best practices.")

    st.info("🚧 Sales Coaching feature - Get AI-powered coaching suggestions and performance insights")

    # Coaching categories
    coaching_areas = [
        "Deal Strategy",
        "Objection Handling",
        "Closing Techniques",
        "Stakeholder Management",
        "Competitive Positioning"
    ]

    selected_area = st.selectbox("Coaching Area", coaching_areas)

    if selected_area:
        st.subheader(f"💡 {selected_area} Recommendations")

        if selected_area == "Deal Strategy":
            st.markdown("""
            **Current Deal Analysis:**
            - Focus on building stronger champion relationship
            - Identify additional stakeholders in decision process
            - Develop compelling business case with ROI metrics

            **Recommended Actions:**
            1. Schedule 1:1 meeting with champion this week
            2. Request introduction to technical decision maker
            3. Prepare customized ROI analysis for their industry
            """)

if __name__ == "__main__":
    if not st.session_state.authenticated:
        login_page()
    else:
        main_app()
