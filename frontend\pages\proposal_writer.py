"""
AI-Powered Proposal Writer page for Streamlit UI
"""
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import json

def show_proposal_writer():
    """AI-powered proposal generation page"""
    st.title("📝 AI-Powered Proposal Writer")
    st.markdown("Generate customized proposals automatically using AI and conversation history.")
    
    # Feature overview
    with st.expander("🌟 Proposal Writer Features", expanded=False):
        st.markdown("""
        - **Context-Aware Generation**: Uses conversation history and CRM data
        - **Template Library**: Pre-built templates for different industries
        - **Dynamic Content**: Automatically adjusts based on customer needs
        - **Pricing Integration**: Includes optimized pricing recommendations
        - **Compliance Check**: Ensures legal and company policy compliance
        - **Multi-format Export**: PDF, Word, PowerPoint formats
        """)
    
    # Main interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("🎯 Proposal Configuration")
        
        # Customer selection
        customers = [
            "TechCorp Inc.",
            "Healthcare Systems Ltd.",
            "Manufacturing Co.",
            "Financial Services Group",
            "Retail Chain Corp."
        ]
        
        selected_customer = st.selectbox(
            "Select Customer",
            customers,
            help="Choose the customer for this proposal"
        )
        
        # Proposal type
        proposal_types = [
            "Enterprise Software Solution",
            "Cloud Migration Services",
            "AI/ML Implementation",
            "Data Analytics Platform",
            "Custom Development"
        ]
        
        proposal_type = st.selectbox(
            "Proposal Type",
            proposal_types,
            help="Select the type of solution being proposed"
        )
        
        # Template selection
        templates = [
            "Standard Enterprise Template",
            "Healthcare Industry Template",
            "Manufacturing Template",
            "Financial Services Template",
            "Custom Template"
        ]
        
        template = st.selectbox(
            "Template",
            templates,
            help="Choose a template that matches your customer's industry"
        )
        
        # Additional context
        st.subheader("📋 Additional Context")
        
        col_ctx1, col_ctx2 = st.columns(2)
        
        with col_ctx1:
            budget_range = st.selectbox(
                "Budget Range",
                ["$100K - $250K", "$250K - $500K", "$500K - $1M", "$1M+"]
            )
            
            timeline = st.selectbox(
                "Implementation Timeline",
                ["1-3 months", "3-6 months", "6-12 months", "12+ months"]
            )
        
        with col_ctx2:
            priority_features = st.multiselect(
                "Priority Features",
                ["Scalability", "Security", "Integration", "Analytics", "Mobile", "Cloud"],
                default=["Scalability", "Security"]
            )
            
            competitors = st.multiselect(
                "Known Competitors",
                ["Competitor A", "Competitor B", "Competitor C", "Other"],
                help="Select known competitors to address in positioning"
            )
        
        # Custom requirements
        custom_requirements = st.text_area(
            "Custom Requirements",
            placeholder="Enter any specific requirements or notes from customer conversations...",
            height=100
        )
        
        # Generate proposal button
        if st.button("🚀 Generate Proposal", use_container_width=True, type="primary"):
            with st.spinner("Generating AI-powered proposal..."):
                import time
                time.sleep(3)  # Simulate processing
                
                # Store proposal data in session state
                st.session_state.generated_proposal = {
                    "customer": selected_customer,
                    "type": proposal_type,
                    "template": template,
                    "budget": budget_range,
                    "timeline": timeline,
                    "features": priority_features,
                    "competitors": competitors,
                    "requirements": custom_requirements,
                    "generated_at": datetime.now()
                }
                
                st.success("✅ Proposal generated successfully!")
                st.rerun()
    
    with col2:
        st.subheader("📊 Recent Proposals")
        
        # Sample recent proposals
        recent_proposals = [
            {
                "customer": "TechCorp Inc.",
                "type": "Enterprise Software",
                "value": "$750K",
                "status": "Sent",
                "date": "2024-01-15"
            },
            {
                "customer": "Healthcare Systems",
                "type": "Cloud Migration",
                "value": "$450K",
                "status": "Under Review",
                "date": "2024-01-12"
            },
            {
                "customer": "Manufacturing Co.",
                "type": "AI Implementation",
                "value": "$600K",
                "status": "Approved",
                "date": "2024-01-10"
            }
        ]
        
        for proposal in recent_proposals:
            status_color = {
                "Sent": "🟡",
                "Under Review": "🔵", 
                "Approved": "🟢",
                "Rejected": "🔴"
            }
            
            st.markdown(f"""
            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;">
                <strong>{proposal['customer']}</strong><br>
                📋 {proposal['type']}<br>
                💰 {proposal['value']}<br>
                {status_color.get(proposal['status'], '⚪')} {proposal['status']}<br>
                📅 {proposal['date']}
            </div>
            """, unsafe_allow_html=True)
        
        st.subheader("📈 Proposal Analytics")
        st.metric("Proposals This Month", "12", "↑ 3")
        st.metric("Average Value", "$525K", "↑ $75K")
        st.metric("Approval Rate", "78%", "↑ 8%")
    
    # Show generated proposal if available
    if hasattr(st.session_state, 'generated_proposal'):
        show_generated_proposal()

def show_generated_proposal():
    """Display the generated proposal"""
    st.markdown("---")
    st.subheader("📄 Generated Proposal")
    
    proposal = st.session_state.generated_proposal
    
    # Proposal tabs
    tab1, tab2, tab3, tab4 = st.tabs(["📋 Executive Summary", "💡 Solution", "💰 Pricing", "📅 Timeline"])
    
    with tab1:
        st.markdown("### Executive Summary")
        
        executive_summary = f"""
        **Proposal for {proposal['customer']}**
        
        We are pleased to present this comprehensive proposal for implementing our {proposal['type']} 
        solution at {proposal['customer']}. Based on our detailed discussions and analysis of your 
        requirements, we have designed a solution that addresses your key priorities:
        
        **Key Benefits:**
        - Enhanced operational efficiency through automation
        - Improved data visibility and analytics capabilities
        - Scalable architecture to support future growth
        - Robust security framework ensuring data protection
        
        **Investment Range:** {proposal['budget']}
        **Implementation Timeline:** {proposal['timeline']}
        
        This proposal outlines a strategic approach to digital transformation that will position 
        {proposal['customer']} for sustained competitive advantage in your industry.
        """
        
        st.markdown(executive_summary)
        
        # Key metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("ROI Expected", "250%", "Within 18 months")
        with col2:
            st.metric("Efficiency Gain", "35%", "Process automation")
        with col3:
            st.metric("Cost Reduction", "25%", "Operational savings")
    
    with tab2:
        st.markdown("### Proposed Solution")
        
        solution_details = f"""
        **Solution Architecture**
        
        Our proposed {proposal['type']} solution is designed specifically for {proposal['customer']}'s 
        unique requirements and includes the following components:
        
        **Core Features:**
        """
        
        for feature in proposal['features']:
            solution_details += f"\n- **{feature}**: Advanced capabilities tailored to your needs"
        
        solution_details += f"""
        
        **Technical Specifications:**
        - Cloud-native architecture for maximum scalability
        - Enterprise-grade security with end-to-end encryption
        - Real-time analytics and reporting dashboard
        - Mobile-responsive interface for remote access
        - API-first design for seamless integrations
        
        **Implementation Approach:**
        1. **Discovery Phase** (Weeks 1-2): Detailed requirements gathering
        2. **Design Phase** (Weeks 3-4): Solution architecture and planning
        3. **Development Phase** (Weeks 5-8): Core system implementation
        4. **Testing Phase** (Weeks 9-10): Quality assurance and user testing
        5. **Deployment Phase** (Weeks 11-12): Go-live and support
        
        **Custom Requirements Addressed:**
        {proposal['requirements'] if proposal['requirements'] else 'Standard implementation approach'}
        """
        
        st.markdown(solution_details)
        
        # Competitive positioning
        if proposal['competitors']:
            st.markdown("**Competitive Advantages:**")
            advantages = {
                "Competitor A": "Superior integration capabilities and faster implementation",
                "Competitor B": "More cost-effective with better long-term ROI",
                "Competitor C": "Advanced AI features and better user experience"
            }
            
            for competitor in proposal['competitors']:
                if competitor in advantages:
                    st.markdown(f"- vs {competitor}: {advantages[competitor]}")
    
    with tab3:
        st.markdown("### Investment & Pricing")
        
        # Pricing breakdown
        pricing_data = {
            "Component": [
                "Software Licenses",
                "Implementation Services",
                "Training & Support",
                "Integration Services",
                "Project Management"
            ],
            "Cost": [150000, 200000, 75000, 100000, 50000],
            "Description": [
                "Enterprise software licenses for 500 users",
                "Custom development and configuration",
                "User training and 1-year support",
                "Third-party system integrations",
                "Dedicated project management"
            ]
        }
        
        pricing_df = pd.DataFrame(pricing_data)
        pricing_df["Cost"] = pricing_df["Cost"].apply(lambda x: f"${x:,}")
        
        st.dataframe(pricing_df, use_container_width=True, hide_index=True)
        
        # Total investment
        total_cost = sum([150000, 200000, 75000, 100000, 50000])
        st.markdown(f"**Total Investment: ${total_cost:,}**")
        
        # Payment terms
        st.markdown("""
        **Payment Terms:**
        - 30% upon contract signing
        - 40% at project milestone completion
        - 30% upon successful go-live
        
        **Included Services:**
        - 12 months of technical support
        - User training for up to 50 users
        - System monitoring and maintenance
        - Regular health checks and optimization
        """)
    
    with tab4:
        st.markdown("### Implementation Timeline")
        
        # Timeline visualization
        timeline_data = {
            "Phase": [
                "Discovery & Planning",
                "Solution Design",
                "Development & Configuration",
                "Testing & Quality Assurance",
                "Deployment & Go-Live",
                "Support & Optimization"
            ],
            "Duration": ["2 weeks", "2 weeks", "4 weeks", "2 weeks", "1 week", "Ongoing"],
            "Start Date": [
                "Week 1",
                "Week 3", 
                "Week 5",
                "Week 9",
                "Week 11",
                "Week 12"
            ],
            "Deliverables": [
                "Requirements document, project plan",
                "Technical architecture, UI/UX designs",
                "Core system, integrations, customizations",
                "Test results, user acceptance testing",
                "Production deployment, user training",
                "Monitoring, support, enhancements"
            ]
        }
        
        timeline_df = pd.DataFrame(timeline_data)
        st.dataframe(timeline_df, use_container_width=True, hide_index=True)
        
        # Key milestones
        st.markdown("""
        **Key Milestones:**
        - Week 2: Requirements sign-off
        - Week 4: Design approval
        - Week 8: Development completion
        - Week 10: User acceptance testing
        - Week 12: Go-live and project completion
        """)
    
    # Action buttons
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("📥 Download PDF", use_container_width=True):
            st.success("PDF download would be implemented here")
    
    with col2:
        if st.button("📧 Email Proposal", use_container_width=True):
            st.success("Email functionality would be implemented here")
    
    with col3:
        if st.button("✏️ Edit Proposal", use_container_width=True):
            st.info("Edit mode would be implemented here")
    
    with col4:
        if st.button("🔄 Regenerate", use_container_width=True):
            del st.session_state.generated_proposal
            st.rerun()
