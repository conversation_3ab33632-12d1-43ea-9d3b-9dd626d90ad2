-- Advanced Conversational AI Sales Assistant
-- 100% Snowflake Native Application Setup
-- Nihilent x Snowflake Hackathon 2025

-- =============================================
-- INITIAL SETUP
-- =============================================

-- Set context
USE ROLE ACCOUNTADMIN;

-- Create database and schema
CREATE DATABASE IF NOT EXISTS SALES_AI_NATIVE;
USE DATABASE SALES_AI_NATIVE;
CREATE SCHEMA IF NOT EXISTS CORE;
USE SCHEMA CORE;

-- Create warehouse for compute
CREATE WAREHOUSE IF NOT EXISTS SALES_AI_WH
WITH 
    WAREHOUSE_SIZE = 'MEDIUM'
    AUTO_SUSPEND = 300
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = FALSE;

USE WAREHOUSE SALES_AI_WH;

-- =============================================
-- ENABLE SNOWFLAKE CORTEX AI
-- =============================================

-- Ensure Cortex AI is enabled (requires ACCOUNTADMIN)
-- This is typically enabled at the account level

-- Test Cortex AI availability
SELECT SNOWFLAKE.CORTEX.COMPLETE(
    'llama2-70b-chat',
    'Hello, this is a test of Snowflake Cortex AI for the sales assistant application.'
) as cortex_test;

-- =============================================
-- CREATE STAGES FOR FILE STORAGE
-- =============================================

-- Stage for voice recordings
CREATE OR REPLACE STAGE voice_stage
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for storing voice recordings for AI processing';

-- Stage for contract documents
CREATE OR REPLACE STAGE contract_stage
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for storing contract documents for analysis';

-- Stage for proposal templates
CREATE OR REPLACE STAGE proposal_stage
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for storing proposal templates and generated proposals';

-- Stage for Streamlit application
CREATE OR REPLACE STAGE streamlit_stage
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for Streamlit in Snowflake application files';

-- Stage for reports
CREATE OR REPLACE STAGE reports_stage
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for generated reports and analytics';

-- =============================================
-- LOAD CORE SCHEMA
-- =============================================

-- Execute the main schema creation
-- (This would reference the tables.sql file)

-- Users table
CREATE OR REPLACE TABLE users (
    user_id STRING PRIMARY KEY,
    username STRING UNIQUE NOT NULL,
    email STRING UNIQUE NOT NULL,
    full_name STRING,
    role STRING DEFAULT 'sales_rep',
    department STRING,
    manager_id STRING,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Companies table
CREATE OR REPLACE TABLE companies (
    company_id STRING PRIMARY KEY,
    company_name STRING NOT NULL,
    industry STRING,
    company_size STRING,
    annual_revenue NUMBER(15,2),
    employee_count INTEGER,
    website STRING,
    headquarters_location STRING,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Opportunities table
CREATE OR REPLACE TABLE opportunities (
    opportunity_id STRING PRIMARY KEY,
    company_id STRING NOT NULL,
    owner_id STRING NOT NULL,
    opportunity_name STRING NOT NULL,
    stage STRING NOT NULL,
    amount NUMBER(15,2),
    probability NUMBER(3,2),
    close_date DATE,
    created_date DATE DEFAULT CURRENT_DATE(),
    last_activity_date DATE,
    source STRING,
    type STRING,
    description TEXT,
    competitor_info TEXT,
    next_steps TEXT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (owner_id) REFERENCES users(user_id)
);

-- Voice recordings table (Cortex AI processed)
CREATE OR REPLACE TABLE voice_recordings (
    recording_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    opportunity_id STRING,
    stage_file_path STRING,
    duration_seconds INTEGER,
    transcript_raw TEXT,
    sentiment_score NUMBER(3,2),
    sentiment_label STRING,
    meeting_summary TEXT,
    crm_fields_extracted VARIANT,
    processed_at TIMESTAMP_NTZ,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id)
);

-- Chat conversations table
CREATE OR REPLACE TABLE chat_conversations (
    conversation_id STRING PRIMARY KEY,
    user_id STRING NOT NULL,
    session_id STRING,
    message_text TEXT NOT NULL,
    response_text TEXT,
    cortex_model STRING DEFAULT 'llama2-70b-chat',
    confidence_score NUMBER(3,2),
    context_data VARIANT,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- =============================================
-- CREATE CORTEX AI FUNCTIONS
-- =============================================

-- Main chat function
CREATE OR REPLACE FUNCTION chat_with_cortex(user_message STRING, context_data VARIANT)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'You are an advanced AI sales assistant built on Snowflake. ',
            'You help sales teams with CRM data, deal analysis, and sales insights. ',
            'Context: ', IFNULL(context_data::STRING, ''), 
            '\n\nUser Question: ', user_message,
            '\n\nProvide a helpful, accurate response based on the sales context.'
        )
    )
$$;

-- Voice processing function
CREATE OR REPLACE FUNCTION process_voice_transcript(transcript TEXT)
RETURNS VARIANT
LANGUAGE SQL
AS
$$
    SELECT OBJECT_CONSTRUCT(
        'sentiment_score', SNOWFLAKE.CORTEX.SENTIMENT(transcript),
        'sentiment_label', 
            CASE 
                WHEN SNOWFLAKE.CORTEX.SENTIMENT(transcript) >= 0.1 THEN 'Positive'
                WHEN SNOWFLAKE.CORTEX.SENTIMENT(transcript) <= -0.1 THEN 'Negative'
                ELSE 'Neutral'
            END,
        'summary', SNOWFLAKE.CORTEX.SUMMARIZE(transcript),
        'crm_fields', PARSE_JSON(
            SNOWFLAKE.CORTEX.COMPLETE(
                'llama2-70b-chat',
                CONCAT(
                    'Extract CRM data from this transcript as JSON: ',
                    transcript,
                    '. Include: customer_name, budget, timeline, next_steps, decision_makers'
                )
            )
        )
    )
$$;

-- Proposal generation function
CREATE OR REPLACE FUNCTION generate_proposal(
    customer_name STRING,
    solution_type STRING,
    customer_context VARIANT
)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'Generate a professional sales proposal for: ', customer_name,
            '\nSolution: ', solution_type,
            '\nContext: ', IFNULL(customer_context::STRING, ''),
            '\nCreate a compelling proposal with executive summary, solution overview, pricing, and next steps.'
        )
    )
$$;

-- =============================================
-- CREATE SNOWPARK ML FUNCTIONS
-- =============================================

-- Lead scoring UDF (simplified for demo)
CREATE OR REPLACE FUNCTION score_lead(
    company_size_score FLOAT,
    industry_score FLOAT,
    revenue_millions FLOAT,
    decision_maker_score FLOAT,
    activity_count FLOAT
)
RETURNS VARIANT
LANGUAGE PYTHON
RUNTIME_VERSION = '3.8'
PACKAGES = ('pandas', 'numpy')
HANDLER = 'score_lead_handler'
AS
$$
def score_lead_handler(company_size_score, industry_score, revenue_millions, decision_maker_score, activity_count):
    # Calculate weighted lead score
    score = (
        company_size_score * 0.2 +
        industry_score * 0.2 +
        min(revenue_millions / 100, 1) * 0.2 +
        decision_maker_score * 0.3 +
        min(activity_count / 10, 1) * 0.1
    ) * 20  # Scale to 0-100
    
    # Determine category
    if score >= 80:
        category = "Hot"
    elif score >= 60:
        category = "Warm"
    elif score >= 40:
        category = "Cold"
    else:
        category = "Unqualified"
    
    return {
        "lead_score": round(score, 2),
        "category": category,
        "confidence": min(score / 100, 1.0)
    }
$$;

-- Deal risk assessment UDF
CREATE OR REPLACE FUNCTION assess_deal_risk(
    deal_value FLOAT,
    days_in_stage INTEGER,
    stakeholder_count INTEGER,
    activity_count INTEGER,
    competition_level STRING
)
RETURNS VARIANT
LANGUAGE PYTHON
RUNTIME_VERSION = '3.8'
PACKAGES = ('pandas', 'numpy')
HANDLER = 'assess_risk_handler'
AS
$$
def assess_risk_handler(deal_value, days_in_stage, stakeholder_count, activity_count, competition_level):
    # Calculate risk factors
    time_risk = min(days_in_stage / 90, 1.0)  # Risk increases with time
    engagement_score = min((stakeholder_count * activity_count) / 20, 1.0)
    
    competition_risk = {
        'None': 0.0,
        'Low': 0.2,
        'Medium': 0.4,
        'High': 0.6,
        'Very High': 0.8
    }.get(competition_level, 0.5)
    
    # Calculate overall risk score (0-100, lower is better)
    risk_score = (time_risk * 0.3 + (1 - engagement_score) * 0.4 + competition_risk * 0.3) * 100
    
    # Determine risk level
    if risk_score <= 30:
        risk_level = "Low"
    elif risk_score <= 60:
        risk_level = "Medium"
    else:
        risk_level = "High"
    
    win_probability = max(0.1, 1.0 - (risk_score / 100))
    
    return {
        "risk_score": round(risk_score, 2),
        "risk_level": risk_level,
        "win_probability": round(win_probability, 2),
        "confidence": 0.85
    }
$$;

-- =============================================
-- CREATE AUTOMATION TASKS
-- =============================================

-- Task for processing voice recordings
CREATE OR REPLACE TASK process_voice_recordings_task
WAREHOUSE = 'SALES_AI_WH'
SCHEDULE = 'USING CRON 0 */2 * * *'  -- Every 2 hours
AS
UPDATE voice_recordings 
SET 
    transcript_processed = process_voice_transcript(transcript_raw):summary::STRING,
    sentiment_score = process_voice_transcript(transcript_raw):sentiment_score::NUMBER,
    sentiment_label = process_voice_transcript(transcript_raw):sentiment_label::STRING,
    crm_fields_extracted = process_voice_transcript(transcript_raw):crm_fields,
    processed_at = CURRENT_TIMESTAMP()
WHERE processed_at IS NULL;

-- Task for lead scoring
CREATE OR REPLACE TASK lead_scoring_task
WAREHOUSE = 'SALES_AI_WH'
SCHEDULE = 'USING CRON 0 9 * * MON'  -- Every Monday at 9 AM
AS
INSERT INTO lead_scores (score_id, company_id, user_id, lead_score, score_category, scored_at)
SELECT 
    CONCAT('score_', company_id, '_', CURRENT_DATE()),
    company_id,
    'system',
    score_lead(
        CASE company_size WHEN 'enterprise' THEN 5 WHEN 'large' THEN 4 ELSE 2 END,
        CASE industry WHEN 'technology' THEN 5 WHEN 'finance' THEN 4 ELSE 3 END,
        COALESCE(annual_revenue, 0) / 1000000,
        4.0,  -- Default decision maker score
        5.0   -- Default activity count
    ):lead_score::NUMBER,
    score_lead(
        CASE company_size WHEN 'enterprise' THEN 5 WHEN 'large' THEN 4 ELSE 2 END,
        CASE industry WHEN 'technology' THEN 5 WHEN 'finance' THEN 4 ELSE 3 END,
        COALESCE(annual_revenue, 0) / 1000000,
        4.0,
        5.0
    ):category::STRING,
    CURRENT_TIMESTAMP()
FROM companies
WHERE company_id NOT IN (
    SELECT company_id FROM lead_scores 
    WHERE scored_at >= CURRENT_DATE() - 7
);

-- =============================================
-- CREATE STREAMS FOR REAL-TIME PROCESSING
-- =============================================

-- Stream for opportunity changes
CREATE OR REPLACE STREAM opportunity_changes_stream 
ON TABLE opportunities;

-- Stream for new voice recordings
CREATE OR REPLACE STREAM voice_recordings_stream 
ON TABLE voice_recordings;

-- =============================================
-- DEPLOY STREAMLIT APPLICATION
-- =============================================

-- Create Streamlit app (this would reference the main.py file)
CREATE OR REPLACE STREAMLIT sales_ai_app
ROOT_LOCATION = '@streamlit_stage'
MAIN_FILE = 'main.py'
QUERY_WAREHOUSE = 'SALES_AI_WH'
COMMENT = 'Advanced Conversational AI Sales Assistant - 100% Snowflake Native';

-- Grant permissions
GRANT USAGE ON STREAMLIT sales_ai_app TO ROLE PUBLIC;

-- =============================================
-- LOAD SAMPLE DATA
-- =============================================

-- Insert sample users
INSERT INTO users (user_id, username, email, full_name, role, department) VALUES
('user_001', 'john.smith', '<EMAIL>', 'John Smith', 'sales_rep', 'Sales'),
('user_002', 'sarah.johnson', '<EMAIL>', 'Sarah Johnson', 'sales_manager', 'Sales'),
('user_003', 'demo_user', '<EMAIL>', 'Demo User', 'sales_rep', 'Sales');

-- Insert sample companies
INSERT INTO companies (company_id, company_name, industry, company_size, annual_revenue, employee_count) VALUES
('comp_001', 'TechCorp Inc.', 'Technology', 'Enterprise', *********, 2500),
('comp_002', 'Healthcare Systems Ltd.', 'Healthcare', 'Large', *********, 1200),
('comp_003', 'Manufacturing Co.', 'Manufacturing', 'Medium', *********, 800);

-- Insert sample opportunities
INSERT INTO opportunities (opportunity_id, company_id, owner_id, opportunity_name, stage, amount, probability, close_date) VALUES
('opp_001', 'comp_001', 'user_001', 'Enterprise AI Platform', 'Proposal', 750000, 0.70, '2024-03-15'),
('opp_002', 'comp_002', 'user_002', 'Healthcare Analytics', 'Negotiation', 450000, 0.80, '2024-02-28'),
('opp_003', 'comp_003', 'user_003', 'Manufacturing Optimization', 'Qualification', 600000, 0.45, '2024-04-30');

-- =============================================
-- START AUTOMATION
-- =============================================

-- Resume tasks
ALTER TASK process_voice_recordings_task RESUME;
ALTER TASK lead_scoring_task RESUME;

-- =============================================
-- VERIFICATION
-- =============================================

-- Test Cortex AI functions
SELECT chat_with_cortex('Hello, show me my pipeline', OBJECT_CONSTRUCT('user_id', 'user_001')) as test_chat;

-- Test lead scoring
SELECT score_lead(5, 5, 100, 5, 10) as test_lead_score;

-- Test deal risk assessment
SELECT assess_deal_risk(750000, 30, 5, 10, 'Medium') as test_risk_assessment;

-- Show created objects
SHOW TABLES;
SHOW FUNCTIONS;
SHOW TASKS;
SHOW STREAMS;
SHOW STAGES;

-- Display success message
SELECT 'Advanced Conversational AI Sales Assistant deployed successfully! 100% Snowflake Native.' as deployment_status;
