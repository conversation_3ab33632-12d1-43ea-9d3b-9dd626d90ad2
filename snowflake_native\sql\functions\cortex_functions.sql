-- Snowflake Cortex AI Functions for Sales Assistant
-- 100% Native Snowflake Implementation

USE DATABASE SALES_AI_NATIVE;
USE SCHEMA CORE;

-- =============================================
-- CONVERSATIONAL AI FUNCTIONS
-- =============================================

-- Main chat function using Snowflake Cortex
CREATE OR REPLACE FUNCTION chat_with_ai(user_message STRING, context_data VARIANT)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'You are an advanced AI sales assistant built on <PERSON>f<PERSON>. ',
            'You help sales teams with CRM data, deal analysis, and sales insights. ',
            'Context: ', IFNULL(context_data::STRING, ''), 
            '\n\nUser Question: ', user_message,
            '\n\nProvide a helpful, accurate response based on the sales context.'
        )
    )
$$;

-- Generate sales proposal content
CREATE OR REPLACE FUNCTION generate_proposal_content(
    customer_name STRING,
    solution_type STRING,
    customer_context VARIANT,
    template_content STRING
)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'Generate a professional sales proposal for: ', customer_name,
            '\nSolution Type: ', solution_type,
            '\nCustomer Context: ', IFNULL(customer_context::STRING, ''),
            '\nTemplate: ', template_content,
            '\n\nCreate a compelling, customized proposal that addresses the customer''s specific needs and demonstrates clear business value.'
        )
    )
$$;

-- Extract CRM fields from voice transcript
CREATE OR REPLACE FUNCTION extract_crm_fields(transcript TEXT)
RETURNS VARIANT
LANGUAGE SQL
AS
$$
    SELECT PARSE_JSON(
        SNOWFLAKE.CORTEX.COMPLETE(
            'llama2-70b-chat',
            CONCAT(
                'Extract CRM-relevant information from this sales call transcript and return as JSON: ',
                transcript,
                '\n\nExtract: customer_name, contact_person, budget_mentioned, timeline, pain_points, next_steps, decision_makers, competitors_mentioned. ',
                'Return only valid JSON format.'
            )
        )
    )
$$;

-- Generate coaching recommendations
CREATE OR REPLACE FUNCTION generate_coaching_advice(
    deal_data VARIANT,
    performance_data VARIANT,
    coaching_area STRING
)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'Provide sales coaching advice for: ', coaching_area,
            '\nDeal Information: ', IFNULL(deal_data::STRING, ''),
            '\nPerformance Data: ', IFNULL(performance_data::STRING, ''),
            '\n\nProvide specific, actionable coaching recommendations to improve sales performance.'
        )
    )
$$;

-- Generate competitive battlecard
CREATE OR REPLACE FUNCTION generate_battlecard(
    competitor_name STRING,
    opportunity_context VARIANT,
    market_data VARIANT
)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'Create a competitive battlecard against: ', competitor_name,
            '\nOpportunity Context: ', IFNULL(opportunity_context::STRING, ''),
            '\nMarket Data: ', IFNULL(market_data::STRING, ''),
            '\n\nInclude: competitive strengths/weaknesses, positioning strategy, key differentiators, objection handling, and win strategies.'
        )
    )
$$;

-- =============================================
-- SENTIMENT & ANALYSIS FUNCTIONS
-- =============================================

-- Analyze call sentiment with detailed breakdown
CREATE OR REPLACE FUNCTION analyze_call_sentiment(transcript TEXT)
RETURNS VARIANT
LANGUAGE SQL
AS
$$
    SELECT OBJECT_CONSTRUCT(
        'overall_sentiment', SNOWFLAKE.CORTEX.SENTIMENT(transcript),
        'sentiment_label', 
            CASE 
                WHEN SNOWFLAKE.CORTEX.SENTIMENT(transcript) >= 0.1 THEN 'Positive'
                WHEN SNOWFLAKE.CORTEX.SENTIMENT(transcript) <= -0.1 THEN 'Negative'
                ELSE 'Neutral'
            END,
        'key_emotions', PARSE_JSON(
            SNOWFLAKE.CORTEX.COMPLETE(
                'llama2-70b-chat',
                CONCAT(
                    'Analyze the emotions and sentiment in this transcript: ',
                    transcript,
                    '\n\nReturn JSON with: dominant_emotion, customer_satisfaction, engagement_level, concern_areas'
                )
            )
        )
    )
$$;

-- Summarize meeting with action items
CREATE OR REPLACE FUNCTION summarize_meeting(transcript TEXT, meeting_type STRING)
RETURNS VARIANT
LANGUAGE SQL
AS
$$
    SELECT OBJECT_CONSTRUCT(
        'summary', SNOWFLAKE.CORTEX.SUMMARIZE(transcript),
        'action_items', PARSE_JSON(
            SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
                transcript,
                'What are the specific action items and next steps mentioned in this meeting? Return as JSON array.'
            )
        ),
        'key_decisions', SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
            transcript,
            'What key decisions or commitments were made during this meeting?'
        ),
        'follow_up_date', SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
            transcript,
            'When is the next meeting or follow-up scheduled?'
        )
    )
$$;

-- =============================================
-- DOCUMENT PROCESSING FUNCTIONS
-- =============================================

-- Analyze contract risk using Cortex AI
CREATE OR REPLACE FUNCTION analyze_contract_risk(contract_text TEXT)
RETURNS VARIANT
LANGUAGE SQL
AS
$$
    SELECT PARSE_JSON(
        SNOWFLAKE.CORTEX.COMPLETE(
            'llama2-70b-chat',
            CONCAT(
                'Analyze this contract for risks and compliance issues: ',
                contract_text,
                '\n\nReturn JSON with: risk_score (1-100), risk_factors (array), compliance_issues (array), recommendations (array), payment_terms, termination_clauses'
            )
        )
    )
$$;

-- Extract key contract terms
CREATE OR REPLACE FUNCTION extract_contract_terms(contract_text TEXT)
RETURNS VARIANT
LANGUAGE SQL
AS
$$
    SELECT OBJECT_CONSTRUCT(
        'payment_terms', SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
            contract_text,
            'What are the payment terms specified in this contract?'
        ),
        'contract_value', SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
            contract_text,
            'What is the total contract value or amount?'
        ),
        'duration', SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
            contract_text,
            'What is the contract duration or term length?'
        ),
        'termination_clause', SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
            contract_text,
            'What are the termination conditions mentioned?'
        ),
        'liability_limits', SNOWFLAKE.CORTEX.EXTRACT_ANSWER(
            contract_text,
            'What liability limitations are specified?'
        )
    )
$$;

-- =============================================
-- REPORTING & ANALYTICS FUNCTIONS
-- =============================================

-- Convert natural language to SQL query
CREATE OR REPLACE FUNCTION nl_to_sql(natural_query STRING, schema_context STRING)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'Convert this natural language query to SQL: ', natural_query,
            '\n\nDatabase Schema Context: ', schema_context,
            '\n\nGenerate only the SQL query without explanations. Use proper table names and joins.'
        )
    )
$$;

-- Generate report narrative from data
CREATE OR REPLACE FUNCTION generate_report_narrative(
    report_data VARIANT,
    report_type STRING
)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'Generate a business narrative for this ', report_type, ' report: ',
            report_data::STRING,
            '\n\nProvide insights, trends, and actionable recommendations based on the data.'
        )
    )
$$;

-- =============================================
-- UTILITY FUNCTIONS
-- =============================================

-- Translate content for global sales teams
CREATE OR REPLACE FUNCTION translate_sales_content(
    content TEXT,
    target_language STRING
)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.TRANSLATE(content, 'en', target_language)
$$;

-- Generate follow-up email content
CREATE OR REPLACE FUNCTION generate_followup_email(
    customer_context VARIANT,
    meeting_summary TEXT,
    email_type STRING
)
RETURNS STRING
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama2-70b-chat',
        CONCAT(
            'Generate a professional follow-up email (',email_type,'): ',
            '\nCustomer Context: ', IFNULL(customer_context::STRING, ''),
            '\nMeeting Summary: ', meeting_summary,
            '\n\nCreate a personalized, professional email that moves the sales process forward.'
        )
    )
$$;

-- Calculate deal health score using multiple factors
CREATE OR REPLACE FUNCTION calculate_deal_health(
    opportunity_data VARIANT,
    activity_data VARIANT,
    stakeholder_data VARIANT
)
RETURNS NUMBER(3,2)
LANGUAGE SQL
AS
$$
    WITH health_factors AS (
        SELECT 
            -- Stage progression score
            CASE 
                WHEN opportunity_data:stage::STRING = 'Closed Won' THEN 1.0
                WHEN opportunity_data:stage::STRING = 'Negotiation' THEN 0.8
                WHEN opportunity_data:stage::STRING = 'Proposal' THEN 0.6
                WHEN opportunity_data:stage::STRING = 'Qualification' THEN 0.4
                ELSE 0.2
            END as stage_score,
            
            -- Activity engagement score
            LEAST(1.0, COALESCE(activity_data:activity_count::NUMBER, 0) / 10.0) as activity_score,
            
            -- Stakeholder engagement score
            LEAST(1.0, COALESCE(stakeholder_data:stakeholder_count::NUMBER, 0) / 5.0) as stakeholder_score,
            
            -- Time factor (deals get riskier over time)
            GREATEST(0.1, 1.0 - (COALESCE(opportunity_data:days_in_stage::NUMBER, 0) / 90.0)) as time_score
    )
    SELECT 
        ROUND(
            (stage_score * 0.4 + activity_score * 0.3 + stakeholder_score * 0.2 + time_score * 0.1),
            2
        )
    FROM health_factors
$$;

-- =============================================
-- EXAMPLE USAGE QUERIES
-- =============================================

/*
-- Example: Chat with AI
SELECT chat_with_ai(
    'What deals are at risk this quarter?',
    OBJECT_CONSTRUCT('user_id', 'user_001', 'role', 'sales_rep')
);

-- Example: Generate proposal
SELECT generate_proposal_content(
    'TechCorp Inc.',
    'Enterprise AI Platform',
    OBJECT_CONSTRUCT('industry', 'Technology', 'size', 'Enterprise'),
    'Standard enterprise template...'
);

-- Example: Analyze sentiment
SELECT analyze_call_sentiment(
    'The customer seemed very interested in our solution and mentioned they have budget approved...'
);

-- Example: Extract CRM fields
SELECT extract_crm_fields(
    'We spoke with John Smith from ABC Corp. They have a budget of $500K and need to implement by Q2...'
);
*/
