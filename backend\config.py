"""
Configuration settings for the Advanced Conversational AI Sales Assistant
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field


class SnowflakeConfig(BaseSettings):
    """Snowflake connection configuration"""
    account: str = Field(..., env="SNOWFLAKE_ACCOUNT")
    user: str = Field(..., env="SNOWFLAKE_USER")
    password: str = Field(..., env="SNOWFLAKE_PASSWORD")
    warehouse: str = Field("COMPUTE_WH", env="SNOWFLAKE_WAREHOUSE")
    database: str = Field("SALES_AI_DB", env="SNOWFLAKE_DATABASE")
    schema: str = Field("PUBLIC", env="SNOWFLAKE_SCHEMA")
    role: Optional[str] = Field(None, env="SNOWFLAKE_ROLE")


class OpenAIConfig(BaseSettings):
    """OpenAI API configuration"""
    api_key: str = Field(..., env="OPENAI_API_KEY")
    model: str = Field("gpt-4-turbo-preview", env="OPENAI_MODEL")
    temperature: float = Field(0.7, env="OPENAI_TEMPERATURE")
    max_tokens: int = Field(2000, env="OPENAI_MAX_TOKENS")


class CRMConfig(BaseSettings):
    """CRM integration configuration"""
    salesforce_client_id: Optional[str] = Field(None, env="SALESFORCE_CLIENT_ID")
    salesforce_client_secret: Optional[str] = Field(None, env="SALESFORCE_CLIENT_SECRET")
    salesforce_username: Optional[str] = Field(None, env="SALESFORCE_USERNAME")
    salesforce_password: Optional[str] = Field(None, env="SALESFORCE_PASSWORD")
    salesforce_security_token: Optional[str] = Field(None, env="SALESFORCE_SECURITY_TOKEN")
    
    hubspot_api_key: Optional[str] = Field(None, env="HUBSPOT_API_KEY")


class VoiceConfig(BaseSettings):
    """Voice processing configuration"""
    whisper_model: str = Field("base", env="WHISPER_MODEL")
    speech_recognition_timeout: int = Field(30, env="SPEECH_TIMEOUT")
    audio_chunk_size: int = Field(1024, env="AUDIO_CHUNK_SIZE")


class AppConfig(BaseSettings):
    """Main application configuration"""
    app_name: str = "Advanced Conversational AI Sales Assistant"
    version: str = "1.0.0"
    debug: bool = Field(False, env="DEBUG")
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")
    
    # Security
    secret_key: str = Field(..., env="SECRET_KEY")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Redis for caching and task queue
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL")
    
    # File storage
    upload_dir: str = Field("uploads", env="UPLOAD_DIR")
    max_file_size: int = Field(10 * 1024 * 1024, env="MAX_FILE_SIZE")  # 10MB
    
    class Config:
        env_file = ".env"
        case_sensitive = False


class Settings:
    """Combined settings class"""
    def __init__(self):
        self.app = AppConfig()
        self.snowflake = SnowflakeConfig()
        self.openai = OpenAIConfig()
        self.crm = CRMConfig()
        self.voice = VoiceConfig()


# Global settings instance
settings = Settings()


# Feature flags
class FeatureFlags:
    """Feature flags for enabling/disabling functionality"""
    VOICE_PROCESSING = True
    PROPOSAL_GENERATION = True
    CONTRACT_ANALYSIS = True
    COMPETITIVE_INTELLIGENCE = True
    DEAL_RISK_ASSESSMENT = True
    AUTOMATED_FOLLOWUP = True
    REPORT_GENERATION = True
    SALES_COACHING = True


features = FeatureFlags()
