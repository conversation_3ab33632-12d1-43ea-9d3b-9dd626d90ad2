-- Sample Data for Advanced Conversational AI Sales Assistant
-- <PERSON><PERSON><PERSON> x Snowflake Hackathon 2025

USE DATABASE SALES_AI_DB;
USE SCHEMA SALES_AI_SCHEMA;

-- =============================================
-- SAMPLE USERS
-- =============================================

INSERT INTO users (user_id, username, email, full_name, role, department) VALUES
('user_001', 'john.smith', '<EMAIL>', '<PERSON>', 'sales_rep', 'Sales'),
('user_002', 'sarah.johnson', '<EMAIL>', '<PERSON>', 'sales_manager', 'Sales'),
('user_003', 'mike.chen', '<EMAIL>', '<PERSON>', 'sales_rep', 'Sales'),
('user_004', 'lisa.brown', '<EMAIL>', '<PERSON>', 'sales_rep', 'Sales'),
('user_005', 'admin', '<EMAIL>', 'System Admin', 'admin', 'IT');

-- =============================================
-- SAMPLE COMPANIES
-- =============================================

INSERT INTO companies (company_id, company_name, industry, company_size, annual_revenue, employee_count, website, headquarters_location) VALUES
('comp_001', 'TechCorp Inc.', 'Technology', 'Enterprise', *********, 2500, 'www.techcorp.com', 'San Francisco, CA'),
('comp_002', 'Healthcare Systems Ltd.', 'Healthcare', 'Large', *********, 1200, 'www.healthsys.com', 'Boston, MA'),
('comp_003', 'Manufacturing Co.', 'Manufacturing', 'Medium', *********, 800, 'www.mfgco.com', 'Detroit, MI'),
('comp_004', 'Financial Services Group', 'Financial Services', 'Enterprise', *********, 3000, 'www.fingroup.com', 'New York, NY'),
('comp_005', 'Retail Chain Corp.', 'Retail', 'Large', *********, 1500, 'www.retailchain.com', 'Chicago, IL'),
('comp_006', 'StartupTech', 'Technology', 'Startup', 5000000, 50, 'www.startuptech.com', 'Austin, TX'),
('comp_007', 'Global Logistics', 'Logistics', 'Enterprise', *********, 2000, 'www.globallog.com', 'Atlanta, GA');

-- =============================================
-- SAMPLE CONTACTS
-- =============================================

INSERT INTO contacts (contact_id, company_id, first_name, last_name, email, phone, title, department, decision_maker_level, champion_score) VALUES
('cont_001', 'comp_001', 'David', 'Wilson', '<EMAIL>', '******-0101', 'CTO', 'Technology', 'Primary', 9),
('cont_002', 'comp_001', 'Jennifer', 'Davis', '<EMAIL>', '******-0102', 'VP Engineering', 'Technology', 'Influencer', 7),
('cont_003', 'comp_002', 'Robert', 'Miller', '<EMAIL>', '******-0201', 'Chief Medical Officer', 'Medical', 'Primary', 8),
('cont_004', 'comp_002', 'Emily', 'Garcia', '<EMAIL>', '******-0202', 'IT Director', 'Technology', 'Technical', 6),
('cont_005', 'comp_003', 'Michael', 'Rodriguez', '<EMAIL>', '******-0301', 'Operations Manager', 'Operations', 'Influencer', 5),
('cont_006', 'comp_004', 'Jessica', 'Martinez', '<EMAIL>', '******-0401', 'CFO', 'Finance', 'Primary', 9),
('cont_007', 'comp_005', 'Christopher', 'Anderson', '<EMAIL>', '******-0501', 'VP Technology', 'Technology', 'Primary', 7),
('cont_008', 'comp_006', 'Amanda', 'Taylor', '<EMAIL>', '******-0601', 'CEO', 'Executive', 'Primary', 8);

-- =============================================
-- SAMPLE OPPORTUNITIES
-- =============================================

INSERT INTO opportunities (opportunity_id, company_id, owner_id, opportunity_name, stage, amount, probability, close_date, created_date, source, type, description) VALUES
('opp_001', 'comp_001', 'user_001', 'Enterprise AI Platform Implementation', 'Proposal', 750000, 0.70, '2024-03-15', '2024-01-10', 'Inbound', 'New Business', 'Complete AI platform implementation for enterprise operations'),
('opp_002', 'comp_002', 'user_002', 'Healthcare Data Analytics Solution', 'Negotiation', 450000, 0.80, '2024-02-28', '2024-01-05', 'Referral', 'New Business', 'Advanced analytics platform for patient data management'),
('opp_003', 'comp_003', 'user_003', 'Manufacturing Process Optimization', 'Qualification', 600000, 0.45, '2024-04-30', '2024-01-15', 'Cold Outreach', 'New Business', 'AI-driven manufacturing process optimization and automation'),
('opp_004', 'comp_004', 'user_001', 'Financial Risk Management System', 'Prospecting', 850000, 0.25, '2024-05-15', '2024-01-20', 'Trade Show', 'New Business', 'Comprehensive risk management and compliance platform'),
('opp_005', 'comp_005', 'user_004', 'Retail Analytics Platform', 'Proposal', 300000, 0.60, '2024-03-01', '2024-01-08', 'Website', 'New Business', 'Customer analytics and inventory optimization platform'),
('opp_006', 'comp_006', 'user_003', 'Startup Growth Analytics', 'Closed Won', 75000, 1.00, '2024-01-25', '2023-12-01', 'Partner', 'New Business', 'Growth analytics and customer acquisition platform'),
('opp_007', 'comp_007', 'user_002', 'Global Supply Chain Optimization', 'Qualification', 950000, 0.40, '2024-06-30', '2024-01-12', 'Inbound', 'New Business', 'AI-powered supply chain optimization and logistics management');

-- =============================================
-- SAMPLE ACTIVITIES
-- =============================================

INSERT INTO activities (activity_id, user_id, opportunity_id, contact_id, activity_type, subject, description, activity_date, duration_minutes, outcome) VALUES
('act_001', 'user_001', 'opp_001', 'cont_001', 'Meeting', 'Technical Requirements Discussion', 'Discussed technical architecture and integration requirements', '2024-01-22', 60, 'Positive'),
('act_002', 'user_001', 'opp_001', 'cont_002', 'Call', 'Follow-up on Proposal', 'Clarified pricing and implementation timeline', '2024-01-24', 30, 'Neutral'),
('act_003', 'user_002', 'opp_002', 'cont_003', 'Meeting', 'Executive Presentation', 'Presented solution to executive team', '2024-01-23', 90, 'Very Positive'),
('act_004', 'user_003', 'opp_003', 'cont_005', 'Call', 'Discovery Call', 'Initial needs assessment and pain point identification', '2024-01-18', 45, 'Positive'),
('act_005', 'user_004', 'opp_005', 'cont_007', 'Email', 'Proposal Submission', 'Sent detailed proposal with pricing options', '2024-01-21', 0, 'Pending');

-- =============================================
-- SAMPLE CHAT CONVERSATIONS
-- =============================================

INSERT INTO chat_conversations (conversation_id, user_id, session_id, message_text, response_text, intent, confidence_score) VALUES
('chat_001', 'user_001', 'sess_001', 'Show me my pipeline for this quarter', 'Here are your Q1 opportunities: 3 active deals worth $2.1M total. TechCorp deal is in proposal stage and needs attention.', 'pipeline_query', 0.95),
('chat_002', 'user_001', 'sess_001', 'What is the risk level for the TechCorp deal?', 'TechCorp deal shows medium risk (70% win probability). Key factors: strong champion, competitive pressure from Competitor A.', 'risk_assessment', 0.92),
('chat_003', 'user_002', 'sess_002', 'Generate a report for my team performance this month', 'Generating team performance report... Your team closed 5 deals worth $1.8M with an 85% win rate this month.', 'report_generation', 0.88),
('chat_004', 'user_003', 'sess_003', 'Create a proposal for Manufacturing Co based on our last meeting', 'I will create a customized proposal for Manufacturing Co. focusing on process optimization and ROI metrics discussed in your meeting.', 'proposal_generation', 0.91);

-- =============================================
-- SAMPLE VOICE RECORDINGS
-- =============================================

INSERT INTO voice_recordings (recording_id, user_id, opportunity_id, contact_id, file_path, duration_seconds, transcript, sentiment_score, meeting_summary) VALUES
('voice_001', 'user_001', 'opp_001', 'cont_001', '/recordings/techcorp_meeting_20240122.wav', 3600, 'Detailed discussion about technical requirements and implementation approach...', 0.75, 'Positive meeting with strong technical alignment. Next steps: technical demo scheduled for next week.'),
('voice_002', 'user_002', 'opp_002', 'cont_003', '/recordings/healthcare_exec_20240123.wav', 5400, 'Executive presentation covering business value and ROI projections...', 0.85, 'Very positive executive meeting. Strong buy-in from leadership. Moving to contract negotiation phase.'),
('voice_003', 'user_003', 'opp_003', 'cont_005', '/recordings/manufacturing_discovery_20240118.wav', 2700, 'Discovery call identifying key pain points in manufacturing processes...', 0.65, 'Good discovery session. Identified automation opportunities. Need to address budget concerns.');

-- =============================================
-- SAMPLE PROPOSALS
-- =============================================

INSERT INTO proposals (proposal_id, opportunity_id, user_id, proposal_name, template_used, status, total_value, generated_at) VALUES
('prop_001', 'opp_001', 'user_001', 'TechCorp Enterprise AI Platform Proposal', 'Enterprise Template', 'sent', 750000, '2024-01-20 10:30:00'),
('prop_002', 'opp_002', 'user_002', 'Healthcare Analytics Solution Proposal', 'Healthcare Template', 'approved', 450000, '2024-01-19 14:15:00'),
('prop_003', 'opp_005', 'user_004', 'Retail Analytics Platform Proposal', 'Retail Template', 'draft', 300000, '2024-01-21 16:45:00');

-- =============================================
-- SAMPLE DEAL RISK ASSESSMENTS
-- =============================================

INSERT INTO deal_risk_assessments (assessment_id, opportunity_id, user_id, risk_score, risk_level, win_probability, model_version, assessed_at) VALUES
('risk_001', 'opp_001', 'user_001', 70, 'Medium', 0.70, 'v1.2', '2024-01-22 09:00:00'),
('risk_002', 'opp_002', 'user_002', 85, 'Low', 0.85, 'v1.2', '2024-01-23 11:30:00'),
('risk_003', 'opp_003', 'user_003', 45, 'High', 0.45, 'v1.2', '2024-01-18 15:20:00'),
('risk_004', 'opp_004', 'user_001', 25, 'High', 0.25, 'v1.2', '2024-01-20 13:45:00'),
('risk_005', 'opp_005', 'user_004', 60, 'Medium', 0.60, 'v1.2', '2024-01-21 10:15:00');

-- =============================================
-- SAMPLE LEAD SCORES
-- =============================================

INSERT INTO lead_scores (score_id, company_id, contact_id, user_id, lead_score, score_category, model_version, scored_at) VALUES
('score_001', 'comp_001', 'cont_001', 'user_001', 85, 'Hot', 'v1.1', '2024-01-10 08:00:00'),
('score_002', 'comp_002', 'cont_003', 'user_002', 90, 'Hot', 'v1.1', '2024-01-05 09:30:00'),
('score_003', 'comp_003', 'cont_005', 'user_003', 65, 'Warm', 'v1.1', '2024-01-15 14:20:00'),
('score_004', 'comp_006', 'cont_008', 'user_003', 75, 'Hot', 'v1.1', '2023-12-01 10:45:00');

-- =============================================
-- SAMPLE COMPETITIVE INTELLIGENCE
-- =============================================

INSERT INTO competitive_intelligence (intelligence_id, competitor_name, opportunity_id, positioning_strategy, last_updated) VALUES
('comp_intel_001', 'Competitor A', 'opp_001', 'Emphasize superior integration capabilities and faster implementation timeline. Highlight better ROI and customer success stories.', '2024-01-22 12:00:00'),
('comp_intel_002', 'Competitor B', 'opp_003', 'Focus on industry-specific expertise and proven manufacturing solutions. Address cost concerns with value-based selling.', '2024-01-18 16:30:00'),
('comp_intel_003', 'Competitor C', 'opp_004', 'Highlight advanced security features and compliance capabilities. Leverage existing financial services partnerships.', '2024-01-20 14:15:00');

-- =============================================
-- SAMPLE COACHING RECOMMENDATIONS
-- =============================================

INSERT INTO coaching_recommendations (recommendation_id, user_id, opportunity_id, coaching_area, recommendation_text, priority_level) VALUES
('coach_001', 'user_001', 'opp_001', 'Stakeholder Management', 'Expand your stakeholder network. You have strong technical champion but need to engage business decision makers.', 'high'),
('coach_002', 'user_003', 'opp_003', 'Objection Handling', 'Prepare responses to budget objections. Focus on ROI and cost savings to justify investment.', 'medium'),
('coach_003', 'user_001', 'opp_004', 'Deal Strategy', 'This deal is early stage. Focus on discovery and building relationships before presenting solutions.', 'high'),
('coach_004', 'user_004', 'opp_005', 'Closing Techniques', 'Deal is ready for closing. Schedule final decision meeting and prepare contract terms.', 'medium');
