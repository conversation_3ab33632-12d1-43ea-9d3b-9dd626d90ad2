"""
Deal Risk Assessment page for Streamlit UI
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import numpy as np

def show_deal_risk_assessment():
    """Deal risk assessment and prediction page"""
    st.title("⚠️ Deal Risk Assessment")
    st.markdown("AI-powered deal health scoring and risk prediction with actionable recommendations.")
    
    # Feature overview
    with st.expander("🌟 Risk Assessment Features", expanded=False):
        st.markdown("""
        - **AI Risk Scoring**: Machine learning models predict deal success probability
        - **Early Warning System**: Proactive alerts for at-risk deals
        - **Actionable Recommendations**: Specific steps to improve deal health
        - **Historical Analysis**: Learn from past wins and losses
        - **Competitive Intelligence**: Factor in competitive threats
        - **Stakeholder Mapping**: Assess decision-maker engagement
        """)
    
    # Main interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("🎯 Deal Assessment")
        
        # Deal selection
        deals = [
            "Enterprise Solution - TechCorp Inc. ($750K)",
            "Cloud Migration - Healthcare Systems ($450K)", 
            "AI Implementation - Manufacturing Co. ($600K)",
            "Data Platform - Financial Services ($850K)",
            "Custom Development - Retail Chain ($300K)"
        ]
        
        selected_deal = st.selectbox(
            "Select Deal to Assess",
            deals,
            help="Choose a deal for risk assessment"
        )
        
        # Deal details input
        st.subheader("📋 Deal Information")
        
        col_deal1, col_deal2 = st.columns(2)
        
        with col_deal1:
            deal_value = st.number_input("Deal Value ($)", min_value=0, value=750000, step=10000)
            deal_stage = st.selectbox(
                "Current Stage",
                ["Prospecting", "Qualification", "Proposal", "Negotiation", "Closed Won", "Closed Lost"]
            )
            days_in_stage = st.number_input("Days in Current Stage", min_value=0, value=15, step=1)
            
        with col_deal2:
            close_date = st.date_input("Expected Close Date", value=datetime.now() + timedelta(days=45))
            customer_budget = st.number_input("Customer Budget ($)", min_value=0, value=800000, step=10000)
            competition_level = st.selectbox(
                "Competition Level",
                ["None", "Low", "Medium", "High", "Very High"]
            )
        
        # Stakeholder information
        st.subheader("👥 Stakeholder Engagement")
        
        col_stake1, col_stake2 = st.columns(2)
        
        with col_stake1:
            stakeholder_count = st.number_input("Number of Stakeholders", min_value=1, value=5, step=1)
            champion_strength = st.selectbox(
                "Champion Strength",
                ["No Champion", "Weak", "Moderate", "Strong", "Very Strong"]
            )
            decision_maker_access = st.selectbox(
                "Decision Maker Access",
                ["No Access", "Limited", "Moderate", "Good", "Excellent"]
            )
            
        with col_stake2:
            meeting_frequency = st.number_input("Meetings per Week", min_value=0.0, value=2.0, step=0.5)
            email_responsiveness = st.selectbox(
                "Email Responsiveness",
                ["Poor", "Below Average", "Average", "Good", "Excellent"]
            )
            technical_fit = st.slider("Technical Fit Score", 1, 10, 8)
        
        # Additional factors
        st.subheader("🔍 Additional Risk Factors")
        
        col_risk1, col_risk2 = st.columns(2)
        
        with col_risk1:
            pricing_pressure = st.selectbox(
                "Pricing Pressure",
                ["None", "Low", "Medium", "High", "Very High"]
            )
            timeline_pressure = st.selectbox(
                "Timeline Pressure",
                ["Flexible", "Moderate", "Tight", "Very Tight", "Unrealistic"]
            )
            
        with col_risk2:
            legal_complexity = st.selectbox(
                "Legal/Compliance Complexity",
                ["Simple", "Moderate", "Complex", "Very Complex"]
            )
            implementation_risk = st.selectbox(
                "Implementation Risk",
                ["Low", "Medium", "High", "Very High"]
            )
        
        # Assess risk button
        if st.button("🔍 Assess Deal Risk", use_container_width=True, type="primary"):
            with st.spinner("Analyzing deal risk factors..."):
                import time
                time.sleep(2)  # Simulate AI processing
                
                # Calculate risk score (mock calculation)
                risk_score = calculate_risk_score(
                    deal_value, deal_stage, days_in_stage, customer_budget,
                    competition_level, stakeholder_count, champion_strength,
                    decision_maker_access, meeting_frequency, technical_fit,
                    pricing_pressure, timeline_pressure
                )
                
                st.session_state.risk_assessment = {
                    "deal": selected_deal,
                    "risk_score": risk_score,
                    "assessment_date": datetime.now(),
                    "factors": {
                        "deal_value": deal_value,
                        "stage": deal_stage,
                        "competition": competition_level,
                        "champion": champion_strength,
                        "technical_fit": technical_fit
                    }
                }
                
                st.success("✅ Risk assessment completed!")
                st.rerun()
    
    with col2:
        st.subheader("🚨 High-Risk Deals")
        
        # Sample high-risk deals
        high_risk_deals = [
            {
                "deal": "Financial Services Platform",
                "value": "$850K",
                "risk": "High",
                "score": 25,
                "issue": "No champion identified"
            },
            {
                "deal": "Retail Chain Implementation", 
                "value": "$300K",
                "risk": "Medium",
                "score": 45,
                "issue": "Pricing pressure"
            },
            {
                "deal": "Healthcare Migration",
                "value": "$450K", 
                "risk": "Medium",
                "score": 55,
                "issue": "Timeline concerns"
            }
        ]
        
        for deal in high_risk_deals:
            risk_color = {"High": "🔴", "Medium": "🟡", "Low": "🟢"}
            
            st.markdown(f"""
            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px;">
                <strong>{deal['deal']}</strong><br>
                💰 {deal['value']}<br>
                {risk_color[deal['risk']]} {deal['risk']} Risk ({deal['score']}%)<br>
                ⚠️ {deal['issue']}
            </div>
            """, unsafe_allow_html=True)
        
        st.subheader("📊 Risk Analytics")
        st.metric("Deals at Risk", "8", "↑ 2")
        st.metric("Average Risk Score", "42%", "↓ 5%")
        st.metric("Intervention Success", "73%", "↑ 8%")
        
        # Risk distribution chart
        risk_data = pd.DataFrame({
            'Risk Level': ['Low', 'Medium', 'High'],
            'Count': [15, 8, 3]
        })
        
        fig = px.pie(risk_data, values='Count', names='Risk Level', 
                    title="Deal Risk Distribution",
                    color_discrete_map={'Low': 'green', 'Medium': 'orange', 'High': 'red'})
        st.plotly_chart(fig, use_container_width=True)
    
    # Show risk assessment results if available
    if hasattr(st.session_state, 'risk_assessment'):
        show_risk_results()

def calculate_risk_score(deal_value, stage, days_in_stage, customer_budget, 
                        competition, stakeholders, champion, decision_access, 
                        meetings, technical_fit, pricing_pressure, timeline_pressure):
    """Calculate mock risk score based on inputs"""
    
    # Stage scoring
    stage_scores = {
        "Prospecting": 20,
        "Qualification": 40, 
        "Proposal": 60,
        "Negotiation": 75,
        "Closed Won": 100,
        "Closed Lost": 0
    }
    
    # Competition impact
    competition_impact = {
        "None": 10,
        "Low": 5,
        "Medium": 0,
        "High": -10,
        "Very High": -20
    }
    
    # Champion strength
    champion_scores = {
        "No Champion": -20,
        "Weak": -10,
        "Moderate": 0,
        "Strong": 10,
        "Very Strong": 20
    }
    
    # Base score from stage
    base_score = stage_scores.get(stage, 50)
    
    # Adjustments
    score = base_score
    score += competition_impact.get(competition, 0)
    score += champion_scores.get(champion, 0)
    score += (technical_fit - 5) * 2  # Technical fit adjustment
    score += min(meetings * 5, 15)  # Meeting frequency bonus
    
    # Budget fit
    if customer_budget > 0:
        budget_fit = deal_value / customer_budget
        if budget_fit > 1.2:
            score -= 15  # Over budget
        elif budget_fit < 0.8:
            score += 10  # Under budget
    
    # Days in stage penalty
    if days_in_stage > 30:
        score -= (days_in_stage - 30) * 0.5
    
    return max(0, min(100, int(score)))

def show_risk_results():
    """Display risk assessment results"""
    st.markdown("---")
    st.subheader("📊 Risk Assessment Results")
    
    assessment = st.session_state.risk_assessment
    risk_score = assessment['risk_score']
    
    # Risk level determination
    if risk_score >= 70:
        risk_level = "Low"
        risk_color = "🟢"
        risk_bg_color = "#d4edda"
    elif risk_score >= 40:
        risk_level = "Medium" 
        risk_color = "🟡"
        risk_bg_color = "#fff3cd"
    else:
        risk_level = "High"
        risk_color = "🔴"
        risk_bg_color = "#f8d7da"
    
    # Risk score display
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown(f"""
        <div style="background-color: {risk_bg_color}; padding: 20px; border-radius: 10px; text-align: center;">
            <h2>{risk_color} {risk_level} Risk</h2>
            <h1>{risk_score}%</h1>
            <p>Win Probability</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        # Risk factors breakdown
        st.markdown("**Key Risk Factors:**")
        factors = assessment['factors']
        
        if factors['competition'] in ['High', 'Very High']:
            st.markdown("🔴 High competitive pressure")
        if factors['champion'] in ['No Champion', 'Weak']:
            st.markdown("🔴 Weak or missing champion")
        if factors['technical_fit'] < 6:
            st.markdown("🔴 Poor technical fit")
        if risk_score >= 70:
            st.markdown("🟢 Strong deal fundamentals")
    
    with col3:
        # Confidence metrics
        st.metric("Confidence Level", "87%", "High")
        st.metric("Data Quality", "92%", "Excellent")
        st.metric("Model Accuracy", "84%", "Good")
    
    # Detailed analysis tabs
    tab1, tab2, tab3, tab4 = st.tabs(["📈 Score Breakdown", "💡 Recommendations", "📋 Action Plan", "📊 Trends"])
    
    with tab1:
        st.subheader("Risk Score Breakdown")
        
        # Score components
        components = {
            "Deal Stage": 25,
            "Stakeholder Engagement": 20,
            "Competitive Position": 15,
            "Technical Fit": 18,
            "Budget Alignment": 12,
            "Timeline Feasibility": 10
        }
        
        # Create horizontal bar chart
        fig = go.Figure(go.Bar(
            x=list(components.values()),
            y=list(components.keys()),
            orientation='h',
            marker_color=['green' if x >= 15 else 'orange' if x >= 10 else 'red' for x in components.values()]
        ))
        
        fig.update_layout(
            title="Risk Score Components",
            xaxis_title="Score Contribution",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Detailed scoring
        st.markdown("**Detailed Scoring:**")
        for component, score in components.items():
            st.markdown(f"- **{component}**: {score}/25 points")
    
    with tab2:
        st.subheader("AI Recommendations")
        
        recommendations = []
        
        if risk_score < 70:
            recommendations.extend([
                "🎯 **Strengthen Champion Relationship**: Schedule 1:1 meeting with key champion",
                "👥 **Expand Stakeholder Network**: Identify and engage additional decision makers",
                "💰 **Address Pricing Concerns**: Prepare value justification and ROI analysis"
            ])
        
        if assessment['factors']['competition'] in ['High', 'Very High']:
            recommendations.append("🏆 **Competitive Strategy**: Develop competitive battlecard and differentiation messaging")
        
        if assessment['factors']['technical_fit'] < 7:
            recommendations.append("🔧 **Technical Validation**: Schedule technical deep-dive session")
        
        recommendations.extend([
            "📞 **Increase Touchpoints**: Schedule weekly check-ins with key stakeholders",
            "📊 **Provide Social Proof**: Share relevant case studies and references",
            "⏰ **Create Urgency**: Highlight business impact of delayed decision"
        ])
        
        for i, rec in enumerate(recommendations, 1):
            st.markdown(f"{i}. {rec}")
    
    with tab3:
        st.subheader("30-Day Action Plan")
        
        action_plan = [
            {
                "week": "Week 1",
                "actions": [
                    "Schedule champion meeting",
                    "Prepare competitive analysis",
                    "Update stakeholder map"
                ],
                "owner": "Sales Rep"
            },
            {
                "week": "Week 2", 
                "actions": [
                    "Conduct technical validation",
                    "Present ROI analysis",
                    "Engage decision makers"
                ],
                "owner": "Sales Rep + SE"
            },
            {
                "week": "Week 3",
                "actions": [
                    "Address objections",
                    "Negotiate terms",
                    "Prepare final proposal"
                ],
                "owner": "Sales Rep + Manager"
            },
            {
                "week": "Week 4",
                "actions": [
                    "Present final proposal",
                    "Handle final negotiations",
                    "Close deal"
                ],
                "owner": "Sales Rep + Executive"
            }
        ]
        
        for week_plan in action_plan:
            with st.expander(f"📅 {week_plan['week']}", expanded=True):
                st.markdown(f"**Owner:** {week_plan['owner']}")
                for action in week_plan['actions']:
                    st.markdown(f"- [ ] {action}")
    
    with tab4:
        st.subheader("Risk Trend Analysis")
        
        # Mock historical data
        dates = pd.date_range(start='2024-01-01', end='2024-01-15', freq='D')
        risk_scores = np.random.normal(risk_score, 5, len(dates))
        risk_scores = np.clip(risk_scores, 0, 100)
        
        trend_data = pd.DataFrame({
            'Date': dates,
            'Risk Score': risk_scores
        })
        
        fig = px.line(trend_data, x='Date', y='Risk Score', 
                     title="Risk Score Trend (Last 15 Days)")
        fig.add_hline(y=70, line_dash="dash", line_color="green", 
                     annotation_text="Low Risk Threshold")
        fig.add_hline(y=40, line_dash="dash", line_color="orange",
                     annotation_text="Medium Risk Threshold")
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Trend insights
        st.markdown("**Trend Insights:**")
        st.markdown("- Risk score has been stable over the past week")
        st.markdown("- Slight improvement after champion meeting on Jan 10")
        st.markdown("- Competitive pressure increased after Jan 12")
    
    # Action buttons
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("📧 Send Alert", use_container_width=True):
            st.success("Risk alert sent to sales manager")
    
    with col2:
        if st.button("📅 Schedule Review", use_container_width=True):
            st.success("Deal review meeting scheduled")
    
    with col3:
        if st.button("📊 Export Report", use_container_width=True):
            st.success("Risk assessment report exported")
    
    with col4:
        if st.button("🔄 Reassess", use_container_width=True):
            del st.session_state.risk_assessment
            st.rerun()
